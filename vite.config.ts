import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vueDevTools from 'vite-plugin-vue-devtools';
import packageJson from './package.json';

const branchName = process.env.BRANCH_NAME || ''
const base = branchName ? `/preview_build/${branchName}/` : './'

// https://vite.dev/config/
export default defineConfig({
  base,
  define: {
    'process.env': JSON.stringify({}),
    'development': JSON.stringify(process.env.NODE_ENV === 'development'),
    'import.meta.env.PACKAGE_VERSION': JSON.stringify(packageJson.version),
    'import.meta.env.DATADOG_APP_ID': JSON.stringify(process.env.DATADOG_APP_ID || ''),
    'import.meta.env.DATADOG_ENV': JSON.stringify(process.env.DATADOG_ENV || process.env.NODE_ENV || 'dev'),
    'import.meta.env.DATADOG_CLIENT_TOKEN': JSON.stringify(process.env.DATADOG_CLIENT_TOKEN || ''),
    'import.meta.env.VITE_TASKS_SERVICE_URL': JSON.stringify(process.env.VITE_TASKS_SERVICE_URL || '/service-tasks-stage'),
    'import.meta.env.VITE_SUMMARIZER_SERVICE_URL': JSON.stringify(process.env.VITE_SUMMARIZER_SERVICE_URL || '/service-summarizer-stage'),
    'import.meta.env.VITE_CONNECTORS_SERVICE_URL': JSON.stringify(process.env.VITE_CONNECTORS_SERVICE_URL || '/service-connectors-stage'),
    'import.meta.env.VITE_JOURNEYS_SERVICE_URL': JSON.stringify(process.env.VITE_JOURNEYS_SERVICE_URL || '/service-journeys-stage'),
    'import.meta.env.VITE_NOTIFICATIONS_SERVICE_URL': JSON.stringify(process.env.VITE_NOTIFICATIONS_SERVICE_URL || '/service-notifications-stage'),
  },
  plugins: [
    vue({
      include: [/\.vue$/, /node_modules\/@services\/.*\.vue$/]
    }),
    vueDevTools(),
    {
      name: 'preserve-init-js-path',
      transformIndexHtml(html) {
        // Replace ./init.js with /init.js in the built index.html
        return html.replace('./init.js', '/init.js');
      },
    },
  ],
  build: {
    outDir: './dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        entryFileNames: 'assets/[name].js', // This ensures your index.js file keeps its name
        //chunkFileNames: 'assets/js/[name]-[hash].js', //Customize chunk names if needed
        assetFileNames: 'assets/[name].[ext]', // Customize asset file names if needed
      },
      external: ['axios'],
    },
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/unit/setup.ts'],
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    port: 5173,
    cors: {
      origin: [
        'https://appv5.stage-ovationcxm.com'
      ],
      credentials: true
    },
    proxy: {
      '/admin/v4': {
        target: 'https://api.stage.goboomtown.com',
        changeOrigin: true,
        secure: true,
        ws: true,
        cookieDomainRewrite: { '*': '' },
        cookiePathRewrite: { '*': '/' }
      },
      '^/journeys/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/tasksmywork/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/tasks-vite/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/connectors/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '^/generative-summarizer/.*': {
        target: 'https://cdn.stage.goboomtown.com',
        changeOrigin: true,
        secure: true
      },
      '/service-tasks-stage': {
        target: 'https://us-central1-stage-microservices-7845.cloudfunctions.net',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-tasks-stage/, '/service-tasks-stage')
      },
      '/service-connectors-stage': {
        target: 'https://s-connregistry-851510a1-5xeedb3yvq-uc.a.run.app',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-connectors-stage/, '')
      },
      '/service-summarizer-stage': {
        target: 'https://s-summarizer-f497a979-c7ca7opsia-uc.a.run.app',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-summarizer-stage/, '')
      },
      '/api': {
        target: 'https://app.stage.goboomtown.com/',
        changeOrigin: true,
        // rewrite: path => path.replace(/^\/admin-api/, ''),
        secure: false,
      },
      '/service-journeys-stage': {
        target: 'https://journeys-5xeedb3yvq-uc.a.run.app',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-journeys-stage/, '')
      },
      '/service-notifications-stage': {
        target: 'https://notify-svc-5xeedb3yvq-uc.a.run.app',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/service-notifications-stage/, '')
      },
    }
  }
})
