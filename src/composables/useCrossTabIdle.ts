import { useBroadcastChannel, useIdle, useStorage, useTimestamp } from '@vueuse/core';
import { ref, watch, type Ref, computed } from 'vue';

// Configuration options for the composable
interface CrossTabIdleOptions {
    idleTimeout: number;
    events?: (keyof WindowEventMap)[];
    backend?: 'localStorage' | 'broadcastChannel';
}

interface CrossTabIdleResult {
    isIdle: Ref<boolean>;
    lastActive: Ref<number>;
    error: Ref<string | null>;
    isTimedOut: Ref<boolean>;
    lastActivityGap: Ref<number>;
    showContinuePopup: Ref<boolean>;
    continueCountdown: Ref<number>;
    showLoggedOutPopup: Ref<boolean>;
    post: (data: { 
        lastActive?: number;
        showContinuePopup?: boolean;
        continueCountdown?: number;
        showLoggedOutPopup?: boolean;
    }) => void;
}

// Fallback to single-tab idle detection
function useSingleTabFallback({
    idleTimeout,
    events,
    errorMessage = null,
}: Omit<CrossTabIdleOptions, 'backend'> & { errorMessage?: string | null }): CrossTabIdleResult {
    const { idle: localIdle, lastActive: localLastActive } = useIdle(idleTimeout, { events });
    const error = ref<string | null>(errorMessage);

    const isTimedOut = computed(() => Date.now() - localLastActive.value > idleTimeout);

    return {
        isIdle: localIdle,
        lastActive: localLastActive,
        error,
        isTimedOut,
        lastActivityGap: ref(0),
        showContinuePopup: ref(false),
        continueCountdown: ref(60),
        showLoggedOutPopup: ref(false),
        post: () => {},
    };
}

/**
 * Detects if the user is idle in a cross-tab environment.
 * @param options - Configuration options for the composable
 * @returns An object containing the idle state, last active timestamp, and error message
 */
export function useCrossTabIdle({
    idleTimeout = 30 * 60 * 1000,
    events = ['mousemove', 'keydown', 'scroll', 'click'],
    backend = 'localStorage',
}: CrossTabIdleOptions): CrossTabIdleResult {
    let errorMessage: string | null = null;
    try {
        if (backend === 'localStorage') {
            return useLocalStorageBackend({ idleTimeout, events });
        } else if (backend === 'broadcastChannel') {
            return useBroadcastChannelBackend({ idleTimeout, events });
        } else {
            errorMessage = 'Invalid backend specified. Falling back to single-tab idle detection.';
        }
    } catch (err) {
        console.error(`Failed to initialize ${backend} backend:`, err);
        errorMessage = `Failed to initialize ${backend}. Idle detection is limited to this tab.`;
    }
    return useSingleTabFallback({ idleTimeout, events, errorMessage });
}

// LocalStorage-based implementation
function useLocalStorageBackend({
    idleTimeout,
    events,
}: Omit<CrossTabIdleOptions, 'backend'>): CrossTabIdleResult {
    const LAST_ACTIVITY_KEY = 'lastActivity';
    const isIdle = ref(false);
    const error = ref<string | null>(null);

    // Check if localStorage is unavailable
    if (!window.localStorage) {
        console.warn('localStorage is unavailable, falling back to single-tab idle detection');
        error.value = 'localStorage is unavailable. Idle detection is limited to this tab.';
        return useSingleTabFallback({ idleTimeout, events, errorMessage: error.value });
    }

    // Use useStorage with a custom serializer for numbers
    const lastActive = useStorage<number>(LAST_ACTIVITY_KEY, Date.now(), localStorage, {
        serializer: {
            read: (value: string) => {
                const parsed = parseInt(value, 10);
                if (isNaN(parsed)) {
                    error.value = 'Error accessing localStorage: Invalid data. Idle detection may be inconsistent.';
                    return Date.now();
                }
                return parsed;
            },
            write: (value: number) => value.toString(),
        },
        writeDefaults: true,
    });

    const { idle: localIdle, lastActive: localLastActive } = useIdle(1000, { events });

    // Update lastActive when local activity is detected
    watch(localLastActive, () => {
        if (!localIdle.value) {
            lastActive.value = Date.now();
        }
    });

    // Check for idle state periodically
    const timestamp = useTimestamp({ interval: 1000 });
    watch(timestamp, () => {
        if (isNaN(lastActive.value)) {
            error.value = 'Error accessing lastActivity: Invalid data. Idle detection may be inconsistent.';
            return;
        }
        const timeSinceLastActivity = Date.now() - lastActive.value;
        isIdle.value = timeSinceLastActivity > idleTimeout;
    });

    const isTimedOut = computed(() => Date.now() - lastActive.value > idleTimeout);

    return {
        isIdle,
        lastActive,
        error,
        isTimedOut,
        lastActivityGap: ref(0),
        showContinuePopup: ref(false),
        continueCountdown: ref(60),
        showLoggedOutPopup: ref(false),
        post: () => {},
    };
}

// BroadcastChannel-based implementation
function useBroadcastChannelBackend({
    idleTimeout,
    events,
}: Omit<CrossTabIdleOptions, 'backend'>): CrossTabIdleResult {
    const CHANNEL_NAME = 'activity';
    const isIdle = ref(false);
    const lastActive = ref(Date.now());
    const error = ref<string | null>(null);
    const isTimedOut = ref(false);
    const lastActivityGap = ref(0);
    const showContinuePopup = ref(false);
    const continueCountdown = ref(60);
    const showLoggedOutPopup = ref(false);

    const { isSupported, data, error: channelError, post: broadcastPost } = useBroadcastChannel<
        { 
            lastActive?: number;
            showContinuePopup?: boolean;
            continueCountdown?: number;
            showLoggedOutPopup?: boolean;
        },
        { 
            lastActive?: number;
            showContinuePopup?: boolean;
            continueCountdown?: number;
            showLoggedOutPopup?: boolean;
        }
    >({
        name: CHANNEL_NAME,
    });

    // Check if BroadcastChannel is supported
    if (!isSupported.value) {
        console.warn('BroadcastChannel is not supported, falling back to single-tab idle detection');
        error.value = 'BroadcastChannel is not supported. Idle detection is limited to this tab.';
        return useSingleTabFallback({ idleTimeout, events, errorMessage: error.value });   
    }

    // Check initial channel error
    if (channelError.value) {
        console.error('BroadcastChannel error:', channelError.value);
        error.value = 'Failed to initialize BroadcastChannel. Idle detection is limited to this tab.';
        return useSingleTabFallback({ idleTimeout, events, errorMessage: error.value });
    }

    // Handle subsequent channel errors
    watch(channelError, (err) => {
        if (err) {
            console.error('BroadcastChannel error:', err);
            error.value = 'Failed to initialize BroadcastChannel. Idle detection is limited to this tab.';
        }
    });

    const { idle: localIdle, lastActive: localLastActive } = useIdle(1000, { events });

    // Broadcast activity to other tabs
    watch(localLastActive, () => {
        if (!localIdle.value) {
            const now = Date.now();
            broadcastPost({ lastActive: now });
            lastActive.value = now;
        }
    });

    // Listen for activity from other tabs
    watch(data, (msg) => {
        console.log('BroadcastChannel data changed:', msg);
        if (!msg) return;
        try {
            const receivedTime = msg.lastActive;
            if (typeof receivedTime === 'number' && !isNaN(receivedTime)) {
                // Only update lastActive if no popups are visible
                if (!showContinuePopup.value && !showLoggedOutPopup.value) {
                    lastActive.value = receivedTime;
                    checkTimeOut();
                }
            }

            // Sync popup states
            if (msg.showContinuePopup !== undefined) {
                console.log('Updating showContinuePopup:', msg.showContinuePopup);
                showContinuePopup.value = msg.showContinuePopup;
            }
            if (msg.continueCountdown !== undefined) {
                console.log('Updating continueCountdown:', msg.continueCountdown);
                continueCountdown.value = msg.continueCountdown;
            }
            if (msg.showLoggedOutPopup !== undefined) {
                console.log('Updating showLoggedOutPopup:', msg.showLoggedOutPopup);
                showLoggedOutPopup.value = msg.showLoggedOutPopup;
            }
        } catch (err) {
            console.error('Failed to process BroadcastChannel message:', err);
            error.value = 'Error processing cross-tab activity.';
        }
    });

    const checkTimeOut = () => {
        // Don't check timeout if any popup is visible
        if (showContinuePopup.value || showLoggedOutPopup.value) {
            console.log('Skipping timeout check due to visible popup');
            return;
        }

        const timeSinceLastActivity = Date.now() - lastActive.value;
        isIdle.value = timeSinceLastActivity > idleTimeout;
        isTimedOut.value = Date.now() - lastActive.value > idleTimeout;
        lastActivityGap.value = (idleTimeout - timeSinceLastActivity) / 1000;
    }

    // Check for global inactivity
    const timestamp = useTimestamp({ interval: 1000 });
    watch(timestamp, checkTimeOut);

    return {
        isIdle,
        lastActive,
        error,
        isTimedOut,
        lastActivityGap,
        showContinuePopup,
        continueCountdown,
        showLoggedOutPopup,
        post: broadcastPost,
    };
}