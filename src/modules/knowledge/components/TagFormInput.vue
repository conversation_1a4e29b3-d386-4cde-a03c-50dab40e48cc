<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import ArticleFormInput from './ArticleFormInput.vue';
import { useMetaStore } from '@/stores/meta';

// Define props
const props = defineProps<{
  article: any;
  isLoading?: boolean;
  isSaving?: boolean;
  isHorizontal?: boolean;
  isEditing?: boolean;
  onSubmit?: (field: string, value: any) => Promise<boolean>;
}>();

// Define emits
const emit = defineEmits<{
  (e: 'update', article: any): void;
}>();

// Get meta store for tags data
const metaStore = useMetaStore();

// Helper to extract tag names from IDs for display
const getTagNameFromId = (id: string) => {
  const tag = metaStore.metaData?.pl__tags?.find((t: any) => t.id === id || t.val === id);
  return tag ? tag.lbl : id;
};

// Compute the display values for the tags
const tagDisplayValues = computed(() => {
  const tagIds = props.article.bc__tags_support || [];
  return tagIds.map((id: string) => getTagNameFromId(id));
});

// Compute if there are any tags to display
const hasTagRestrictions = computed(() => {
  return props.article.bc__tags_support && props.article.bc__tags_support.length > 0;
});

// Filter tags to only show those with category === 3
const filteredTagOptions = computed(() => {
  if (!metaStore.metaData?.pl__tags) return [];
  return metaStore.metaData.pl__tags.filter((tag: any) => tag.category === 3);
});

// Track the edited value locally
const editedTags = ref<string[]>([]);

// Initialize the edited tags when the article changes
watch(() => props.article.bc__tags_support, (newTags) => {
  editedTags.value = newTags || [];
}, { immediate: true });

// Track loading state locally
const isLocalSaving = ref(false);

// Reference to the ArticleFormInput component
const formInputRef = ref<InstanceType<typeof ArticleFormInput> | null>(null);

// Handle the update event from ArticleFormInput
const handleUpdate = (fieldName: string, value: any) => {
  editedTags.value = value;
};

// Handle submission start
const handleSubmitStart = () => {
  isLocalSaving.value = true;
};

// Handle the save event from ArticleFormInput
const handleSave = async (fieldName: string, value: any) => {
  try {
    // Check if the arrays are different
    const currentTags = props.article.bc__tags_support || [];
    const isChanged = 
      value.length !== currentTags.length ||
      value.some((id: string) => !currentTags.includes(id)) ||
      currentTags.some((id: string) => !value.includes(id));
    
    // Only submit if the values have changed
    if (isChanged && props.onSubmit) {
      const success = await props.onSubmit('bc__tags_support', value);
      formInputRef.value?.handleSaveComplete(success);
    } else {
      // No change needed, just complete
      formInputRef.value?.handleSaveComplete(true);
    }
  } catch (error) {
    console.error('Error saving tags:', error);
    formInputRef.value?.handleSaveComplete(false);
  } finally {
    isLocalSaving.value = false;
  }
};

// Handle cancel event
const handleCancel = () => {
  // Reset the edited value back to the original
  editedTags.value = props.article.bc__tags_support || [];
};

// Only show the component if the article has the bc__tags_support field
const showComponent = computed(() => {
  return !!props.article.bc__tags_support;
});
</script>

<template>
  <ArticleFormInput
    ref="formInputRef"
    v-if="showComponent"
    label="Tags"
    fieldName="tags"
    :value="article.bc__tags_support || []"
    :displayValue="tagDisplayValues"
    inputType="multiselect"
    displayType="chips"
    :options="filteredTagOptions"
    optionLabel="lbl"
    optionValue="id"
    :isLoading="isLoading || metaStore.isLoading"
    :isHorizontal="isHorizontal"
    :isSaving="isLocalSaving || isSaving"
    :isEditing="isEditing"
    iconClass="pi pi-tags"
    noValueText="No tags"
    dataTestId="article-tags"
    showFilter
    filterPlaceholder="Search tags..."
    enforceSubmitButton
    @update="handleUpdate"
    @save="handleSave"
    @submit-start="handleSubmitStart"
    @cancel="handleCancel"
  />
</template> 