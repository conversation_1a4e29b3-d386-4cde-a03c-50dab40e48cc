<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import BravoDialog from '@services/ui-component-library/components/BravoDialog.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import BravoMultiSelect from '@services/ui-component-library/components/BravoMultiSelect.vue';
import { useKnowledgeAPI } from '@/composables/services/useKnowledgeAPI';
import { useToast } from 'primevue/usetoast';

const toast = useToast();
const isLoading = ref(false);
const labels = ref<any[]>([]);
const knowledgeAPI = useKnowledgeAPI();

const props = defineProps<{
    visible: boolean;
    selectedLabels?: string[];
    selectedArticles?: any[];
}>();

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'save', labels: string[]): void;
}>();

const { t } = useI18n();

const isVisible = ref(props.visible);
const selectedLabels = ref<string[]>(props.selectedLabels || []);

const showLibraryAlert = ref(false);
const differentLibraries = ref<string[]>([]);

const convertToTree = (labels: any[]) => {
    const tree: any[] = [];
    const map: { [key: string]: any } = {};

    // First pass: create all nodes
    labels.forEach((label) => {
        map[label.val] = {
            ...label,
            children: [],
            key: label.val,
            label: label.lbl,
            data: label,
        };
    });

    // Second pass: build the tree
    labels.forEach((label) => {
        const node = map[label.val];
        const pathParts = label.path.split('/');

        if (pathParts.length === 1) {
            // Root level node
            tree.push(node);
        } else {
            // Find parent node
            const parentPath = pathParts.slice(0, -1).join('/');
            const parentLabel = labels.find((l) => l.path === parentPath);
            if (parentLabel) {
                map[parentLabel.val].children.push(node);
            }
        }
    });

    return tree;
};

// Watch for changes in props.visible
watch(
    () => props.visible,
    async (newValue) => {
        isVisible.value = newValue;

        if (newValue) {
            if (props.selectedArticles) {
                // Check if all selected articles are from the same library
                if (props.selectedArticles.length > 1) {
                    const firstLibrary = props.selectedArticles[0].library_name;
                    const allSameLibrary = props.selectedArticles.every(
                        (article) => article.library_name === firstLibrary
                    );
                    if (!allSameLibrary) {
                        // Collect unique library names
                        const libs = [
                            ...new Set(props.selectedArticles.map(a => a.library_name))
                        ];
                        differentLibraries.value = libs;
                        showLibraryAlert.value = true;
                        return;
                    }
                }

                isLoading.value = true;
                try {
                    const query = {
                        sAction: 'metaKBLabels',
                        filter: JSON.stringify([
                            {
                                property: 'root_kb_id',
                                value: props.selectedArticles[0].root_parent_id,
                            },
                        ]),
                    };
                    const response = await knowledgeAPI.loadLabels(query);
                    if (response && Array.isArray(response.pl__kb_labels)) {
                        labels.value = convertToTree(response.pl__kb_labels);
                        // Set selectedLabels from the first record's bc__tags_object_kb_labels
                        selectedLabels.value = props.selectedArticles[0].bc__tags_object_kb_labels || [];
                    }
                } finally {
                    isLoading.value = false;
                }
            }
        }
    }
);

// Watch for changes in isVisible
watch(
    () => isVisible.value,
    (newValue) => {
        emit('update:visible', newValue);
    }
);

const onHide = () => {
    selectedLabels.value = [];
    isVisible.value = false;
    isLoading.value = false;
};

const onSave = async () => {
    isLoading.value = true;
    const updatedArticles = props.selectedArticles?.map((article) => ({
        id: article.id,
        _bulkActionSave: true,
        bc__products_support: selectedLabels.value,
    }));

    if (!updatedArticles || updatedArticles.length === 0) {
        onHide();
        return;
    }

    const query = [
        {
            _bulkActionSave: true,
            bc__tags_object_kb_labels: selectedLabels.value,
            id: props.selectedArticles?.[0].id,
        },
    ];
    try {
        const response = await knowledgeAPI.updateProducts(query);

        if (response.success) {
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Labels updated successfully',
                life: 3000,
            });
            emit('save', selectedLabels.value);
            onHide();
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: response.message || 'Failed to update labels',
                life: 3000,
            });
        }
    } catch (error) {
        console.error('Error updating labels:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update labels',
            life: 3000,
        });
    } finally {
        isLoading.value = false;
    }
};
</script>

<template>
    <BravoDialog
        v-model:visible="isVisible"
        :header="t('knowledge.actionsMenu.update_labels')"
        :modal="true"
        class="knowledge-dialog"
        :class="'update-labels-dialog'"
        @hide="onHide"
    >
        <div class="dialog-content">
            <div class="form-field">
                <label>Labels</label>
                <BravoMultiSelect
                    v-model="selectedLabels"
                    class="label-dropdown"
                    placeholder="Select labels..."
                    :options="labels"
                    optionLabel="label"
                    optionValue="key"
                    display="chip"
                    selectionMode="multiple"
                    :filter="true"
                    :showClear="true"
                    :showToggleAll="true"
                    :panelClass="'p-multiselect-panel'"
                    :loading="isLoading"
                />
            </div>
        </div>
        <template #footer>
            <BravoButton :label="t('common.cancel')" severity="secondary" @click="onHide" />
            <BravoButton :label="t('common.save')" :loading="isLoading" @click="onSave" />
        </template>
    </BravoDialog>
    <BravoDialog
        v-model:visible="showLibraryAlert"
        header="Invalid Selection"
        modal
        class="knowledge-dialog"
        :closable="false"
    >
        <div>
            <p>Update label is not valid on different library items. Select items from the same library and try again.</p>
            <p><b>Selected articles from the following libraries:</b></p>
            <ul>
                <li v-for="(lib, idx) in differentLibraries" :key="lib"><i>{{ idx + 1 }}. {{ lib }}</i></li>
            </ul>
        </div>
        <template #footer>
            <BravoButton label="OK" @click="showLibraryAlert = false; onHide();" />
        </template>
    </BravoDialog>
</template>

<style>
@import './dialogs.css';
</style>
