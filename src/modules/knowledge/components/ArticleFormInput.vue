<script setup lang="ts">
import { ref, watch, onBeforeUnmount, onMounted } from 'vue';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import Dropdown from 'primevue/dropdown';
import MultiSelect from 'primevue/multiselect';
import BravoTag from '@services/ui-component-library/components/BravoTag.vue';
import BravoLabel from '@services/ui-component-library/components/BravoLabel.vue';

// Define the input types we support
type InputType = 'text' | 'dropdown' | 'multiselect';
type DisplayType = 'text' | 'chips' | 'tag';

const props = defineProps<{
  label: string;
  fieldName: string;
  value: any;
  displayValue: string | string[];
  inputType: InputType;
  displayType: DisplayType; 
  options?: any[];
  optionLabel?: string;
  optionValue?: string;
  isLoading?: boolean;
  isHorizontal?: boolean;
  iconClass?: string;
  isSaving?: boolean;
  noValueText?: string;
  dataTestId?: string;
  filterPlaceholder?: string;
  showFilter?: boolean;
  enforceSubmitButton?: boolean;
  isEditing?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update', fieldName: string, value: any): void;
  (e: 'save', fieldName: string, value: any): void;
  (e: 'submit-start'): void;
  (e: 'cancel'): void;
  (e: 'filter', event: any): void;
}>();

const isEditingInternal = ref(false);
const editedValue = ref<any>(null);
const multiSelectRef = ref<any>(null);

// Initialize the edited value when the props value changes
watch(() => props.value, (newValue) => {
  // if (!isEditing.value) {
    // Make a deep copy of array values to avoid reference issues
    editedValue.value = Array.isArray(newValue) ? [...newValue] : newValue;
  // }
}, { immediate: true });

// Start editing the field
const startEditing = () => {
  // Only allow editing if isEditing prop is true (not undefined or false)
  if (props.isEditing !== true) return;
  
  // Make a fresh copy of the value to edit
  editedValue.value = Array.isArray(props.value) ? [...props.value] : props.value;
  isEditingInternal.value = true;
  emit('update', props.fieldName, editedValue.value);
};

// Save the edited value
const saveEdit = () => {
  // Ensure we pass the correct data type for arrays
  const valueToSave = editedValue.value;
  
  // Emit that submission is starting
  emit('submit-start');
  
  // Emit the save event with the value
  emit('save', props.fieldName, valueToSave);
  
  // NOTE: We don't exit edit mode here anymore
  // isEditing.value = false;
  // This will be handled by the parent component when it calls our "handleSaveComplete" method
};

// New method to handle save completion
const handleSaveComplete = (success: boolean = true) => {
  if (success) {
    isEditingInternal.value = false;
  }
  // If not successful, stay in edit mode
};

// Cancel editing
const cancelEdit = () => {
  isEditingInternal.value = false;
  emit('cancel');
};

// Helper function to check if an element is part of a multiselect dropdown
const isInsideMultiSelect = (element: HTMLElement | null): boolean => {
  if (!element) return false;
  
  // Check for common MultiSelect class names in the element or its parents
  return Boolean(
    element.classList.contains('p-multiselect-item') || 
    element.classList.contains('p-checkbox') ||
    element.classList.contains('p-multiselect-header') ||
    element.classList.contains('p-multiselect-filter-container') ||
    (element.parentElement && isInsideMultiSelect(element.parentElement))
  );
};

// Handle click outside - only save automatically if enforceSubmitButton is false
const handleClickOutside = (event: MouseEvent) => {
  if (isEditingInternal.value) {
    const target = event.target as HTMLElement;
    const editField = document.querySelector(`.${props.fieldName}-edit-field`);
    const dropdownPanel = document.querySelector('.p-dropdown-panel, .p-multiselect-panel');
    
    // Check if clicked element is inside the multiselect items
    const isMultiSelectItem = isInsideMultiSelect(target);
    
    // Don't close if we're clicking inside the MultiSelect items, panel, or our edit field
    if (editField && !editField.contains(target) && 
        !(dropdownPanel && dropdownPanel.contains(target)) &&
        !isMultiSelectItem) {
      if (!props.enforceSubmitButton) {
        // Only auto-save if explicit submission is not required
        saveEdit();
      } else {
        // Otherwise just close the dropdown without saving
        cancelEdit();
      }
    }
  }
};

// Add click event listener when editing starts
watch(isEditingInternal, (newValue) => {
  if (newValue) {
    // Use a slight delay to add the click listener to avoid immediate triggering
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 100);
  } else {
    document.removeEventListener('click', handleClickOutside);
  }
});

// Clean up event listener when component is unmounted
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});

// Function to make the entire row clickable in the dropdown
onMounted(() => {
  // We need to wait until the component is mounted and then add a click handler
  // for the dropdown items after PrimeVue has initialized them
  setTimeout(() => {
    makeDropdownRowsClickable();
  }, 200);
});

// Make dropdown rows clickable when editing starts
watch(isEditingInternal, (newValue) => {
  if (newValue) {
    // Apply after dropdown is fully rendered
    setTimeout(() => {
      makeDropdownRowsClickable();
    }, 200);
  }
});

// Function to add click handlers to make the entire multiselect item rows clickable
const makeDropdownRowsClickable = () => {
  // Wait for the dropdown to be visible in the DOM
  const observer = new MutationObserver((mutations) => {
    // Look for the multiselect panel in the DOM
    const panel = document.querySelector('.p-multiselect-panel');
    if (panel) {
      // Find all items in the panel
      const items = panel.querySelectorAll('.p-multiselect-item');
      
      // For each item, add a click handler to the whole row
      items.forEach(item => {
        // Remove existing handler if any
        item.removeEventListener('click', rowClickHandler);
        // Add new handler
        item.addEventListener('click', rowClickHandler);
      });
      
      // Disconnect observer once we've processed the dropdown
      observer.disconnect();
    }
  });
  
  // Start observing the document for dropdown panel
  observer.observe(document.body, { childList: true, subtree: true });
};

// Handle click on a dropdown row
const rowClickHandler = (event: Event) => {
  const target = event.currentTarget as HTMLElement;
  
  // If we're not clicking directly on the checkbox, find and click it
  if (!(event.target as HTMLElement).closest('.p-checkbox')) {
    const checkbox = target.querySelector('.p-checkbox');
    if (checkbox) {
      // Programmatically click the checkbox
      (checkbox as HTMLElement).click();
    }
  }
};

// Helper function to check if an item is selected
const isItemSelected = (item: any) => {
  if (!Array.isArray(editedValue.value)) return false;
  
  if (props.optionValue) {
    const itemValue = item[props.optionValue];
    return editedValue.value.includes(itemValue);
  }
  
  return editedValue.value.includes(item);
};

// Toggle selection for an item
const toggleSelection = (item: any, event: Event) => {
  event.preventDefault();
  event.stopPropagation();
  
  // Get the value to toggle
  const itemValue = props.optionValue ? item[props.optionValue] : item;
  
  // If not an array, initialize it
  if (!Array.isArray(editedValue.value)) {
    editedValue.value = [];
  }
  
  // Check if the item is already selected
  const index = editedValue.value.indexOf(itemValue);
  
  // Toggle the selection
  if (index === -1) {
    // Add the item
    editedValue.value = [...editedValue.value, itemValue];
  } else {
    // Remove the item
    editedValue.value = editedValue.value.filter((val: any) => val !== itemValue);
  }
};

const clearFilter = () => {
  if (multiSelectRef.value && typeof multiSelectRef.value.filterValue !== 'undefined') {
    multiSelectRef.value.filterValue = '';
  }
};

defineExpose({
  handleSaveComplete,
  clearFilter
});
</script>

<template>
  <div 
    class="data-field" 
    :class="{ 'horizontal': isHorizontal }" 
    :data-testid="dataTestId || `article-${fieldName}-field`"
  >
    <BravoLabel :text="label" class="article-field-label" />
    <div 
      :class="[
        'field-value', 
        'editable-field', 
        `${fieldName}-edit-field`,
        { 'field-section-disabled': isEditing !== true }
      ]" 
      @click="startEditing"
      :data-testid="`article-${fieldName}-value`"
    >
      <!-- Display mode -->
      <div 
        v-if="!isEditingInternal" 
        class="display-mode" 
        :class="[
          `${fieldName}-display-mode`,
          { 'field-disabled': isEditing !== true }
        ]" 
        @click.stop="startEditing"
      >
        <i v-if="iconClass" :class="iconClass" />
        
        <!-- Text display -->
        <span v-if="displayType === 'text'" class="clickable-value">
          {{ typeof displayValue === 'string' ? displayValue : (noValueText || 'No value') }}
        </span>
        
        <!-- Chips display -->
        <div v-else-if="displayType === 'chips'" class="chips-content">
          <span v-if="!Array.isArray(displayValue) || displayValue.length === 0">{{ noValueText || 'No items' }}</span>
          <div v-else class="chip-items">
            <BravoTag
              v-for="(item, index) in displayValue"
              :key="index"
              :value="item"
              severity="info"
              class="chip-item"
              @click.stop="startEditing"
              :data-testid="`${fieldName}-chip-${index}`"
            />
          </div>
        </div>
        
        <!-- Tag display (single value with icon) -->
        <div v-else-if="displayType === 'tag'" class="tag-content">
          <span>{{ displayValue }}</span>
        </div>
      </div>
      
      <!-- Edit mode -->
      <div v-else class="edit-container-compact">
        <div class="input-wrapper">
          <!-- Text input -->
          <input 
            v-if="inputType === 'text'"
            type="text" 
            v-model="editedValue"
            class="edit-input"
            @click.stop
            :data-testid="`${dataTestId || fieldName}-input`"
          />
          
          <!-- Dropdown input -->
          <Dropdown 
            v-else-if="inputType === 'dropdown'"
            v-model="editedValue" 
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            class="edit-dropdown"
            :loading="isLoading"
            :data-testid="`${dataTestId || fieldName}-dropdown`"
          />
          
          <!-- MultiSelect input with customized item template -->
          <MultiSelect 
            v-else-if="inputType === 'multiselect'"
            ref="multiSelectRef"
            v-model="editedValue" 
            :options="options"
            :optionLabel="optionLabel"
            :optionValue="optionValue"
            display="chip"
            class="edit-multiselect"
            :loading="isLoading"
            :filter="showFilter !== false"
            :filterPlaceholder="filterPlaceholder || `Search ${label}`"
            :data-testid="`${dataTestId || fieldName}-multiselect`"
            @filter="$emit('filter', $event)"
          >
            <!-- Customize the item template for better click handling -->
            <template #option="slotProps">
              <div 
                class="custom-item-row"
                :class="{ 'custom-item-selected': isItemSelected(slotProps.option) }"
              >
                <div 
                  class="custom-item-clickable-area"
                  @click.stop="toggleSelection(slotProps.option, $event)"
                ></div>
                <div class="custom-item-label" @click.stop="toggleSelection(slotProps.option, $event)">
                  {{ optionLabel ? slotProps.option[optionLabel] : slotProps.option }}
                </div>
              </div>
            </template>
            <!-- Pass through emptyfilter slot from parent component -->
            <template #emptyfilter>
              <slot name="emptyfilter"></slot>
            </template>
            <!-- Pass through footer slot from parent component -->
            <template #footer>
              <slot name="footer"></slot>
            </template>
          </MultiSelect>
        </div>
        
        <div class="edit-actions">
          <Button
            v-if="!isSaving"
            icon="pi pi-check"
            class="p-button-text"
            @mousedown.stop
            @click.stop="saveEdit"
            :data-testid="`save-${dataTestId}-btn`"
          />
          <Button
            v-if="!isSaving"
            icon="pi pi-times"
            class="p-button-text p-button-danger"
            @mousedown.stop
            @click="cancelEdit"
            :data-testid="`cancel-${dataTestId}-edit-btn`"
          />
          <ProgressSpinner
            v-if="isSaving"
            style="width: 1.5rem; height: 1.5rem"
            strokeWidth="4"
            :data-testid="`${dataTestId}-saving-spinner`"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.data-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.data-field.horizontal {
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 0.5rem;
}

.data-field.horizontal .field-value {
  flex: 0 0 70%;
}

.data-field .field-value {
  font-size: 0.813rem;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  width: 100%;
}

.data-field .field-value i {
  color: #64748b;
  font-size: 1rem;
}

.field-value.editable-field {
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  margin: -4px -8px;
  padding: 4px 8px;
  border-radius: 4px;
  width: 100%;
}

.field-value.editable-field:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Prevent hover effect when field contains disabled content */
.field-value.editable-field:hover:has(.field-disabled) {
  background-color: transparent !important;
}

.edit-container-compact {
  display: flex;
  width: 100%;
  align-items: flex-start;
  gap: 8px;
}

.input-wrapper {
  flex: 1;
  position: relative;
  width: calc(100% - 76px);
  overflow: hidden;
}

.edit-input {
  padding: 4px 8px;
  border: 1px solid var(--surface-300, #ddd);
  border-radius: 4px;
  font-size: 0.813rem;
  width: 100%;
  box-sizing: border-box;
}

.edit-dropdown, .edit-multiselect {
  width: 100%;
  font-size: 0.813rem;
}

/* Fix PrimeVue components width */
.edit-dropdown :deep(.p-dropdown),
.edit-multiselect :deep(.p-multiselect) {
  width: 100% !important;
  max-width: 100%;
}

/* Control multiselect chips container */
.edit-multiselect :deep(.p-multiselect-label) {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Prevent multiselect chips from wrapping */
.edit-multiselect :deep(.p-multiselect-token-label) {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Custom item styling for clickable rows */
:deep(.custom-item-row) {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
  transition: background-color 0.2s;
  width: 100%;
  position: relative;
  min-height: 38px;
}

:deep(.custom-item-clickable-area) {
  position: absolute;
  inset: 0;
  cursor: pointer;
  z-index: 1;
}

:deep(.custom-item-row:hover .custom-item-clickable-area) {
  z-index: 3;
}

:deep(.custom-item-row:hover) {
  background-color: var(--surface-200);
}

:deep(.custom-item-selected) {
  background-color: var(--surface-100);
}

:deep(.custom-checkbox) {
  width: 18px;
  height: 18px;
  min-width: 18px; /* Prevent shrinking */
  border: 1px solid var(--surface-400);
  border-radius: 3px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-0);
  position: relative;
  z-index: 2;
}

:deep(.custom-item-selected .custom-checkbox) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--primary-contrast-color, white);
}

:deep(.custom-item-label) {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  z-index: 2;
  margin-left: 6px;
}

.edit-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
  flex: 0 0 68px;
  width: 68px;
  min-width: 68px;
  min-height: 40px;
}

.edit-actions button {
  flex: 0 0 32px;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.display-mode {
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
  border-radius: 4px;
  padding: 4px;
  gap: 0.5rem;
}

.display-mode i {
  margin-top: 2px;
}

.clickable-value {
  cursor: pointer;
}

.chips-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.chips-content > span {
  font-size: 14px;
}

.chip-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.chip-item {
  cursor: pointer;
  transition: transform 0.1s ease;
}

.chip-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* PrimeVue Multiselect Filter Styling */
:deep(.p-multiselect-filter-container) {
  margin-bottom: 0.5rem;
}

:deep(.p-multiselect-filter) {
  width: 100%;
  padding: 0.5rem;
  font-size: 0.813rem;
}

:deep(.p-multiselect-panel) {
  max-height: 400px;
  overflow-y: auto;
}

// Add these new styles for BravoLabel
.article-field-label {
  flex: 0 0 30%;
  font-size: 0.75rem;
  color: #64748b;
  display: block;
  margin-bottom: 0.25rem;
}

.data-field.horizontal .article-field-label {
  margin-bottom: 0;
  padding-top: 0.25rem;
}

/* Disabled field styles */
.field-disabled {
    cursor: default !important;
    opacity: 1;
    pointer-events: none;
}

.field-disabled:hover {
    background-color: transparent !important;
}

/* Also prevent hover on chip items when disabled */
.field-disabled .chip-item:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Prevent pointer cursor on disabled fields and all their children */
.field-disabled,
.field-disabled *,
.field-disabled .clickable-value,
.field-disabled .chip-item,
.field-disabled .display-mode {
    cursor: default !important;
}

/* Disable entire field section when not editable */
.field-section-disabled {
    pointer-events: none !important;
    cursor: default !important;
    opacity: 1;
}

.field-section-disabled:hover {
    background-color: transparent !important;
}
</style> 