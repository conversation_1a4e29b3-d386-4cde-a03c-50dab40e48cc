<script setup lang="ts">
import { ref, onMounted, computed, watchEffect, watch } from 'vue'
import { useCasesStore } from '../../../stores/cases'
import { useUserStore } from '@/stores/user'
import BravoProgressSpinner from '@services/ui-component-library/components/BravoProgressSpinner.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTitle1 from '@services/ui-component-library/components/BravoTypography/BravoTitle1.vue'
import BravoTitle3 from '@services/ui-component-library/components/BravoTypography/BravoTitle3.vue'
import BravoSubhead from '@services/ui-component-library/components/BravoTypography/BravoSubhead.vue'
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue'
import BravoBodyBold from '@services/ui-component-library/components/BravoTypography/BravoBodyBold.vue'
import BravoFilterSelect from '@services/ui-component-library/components/BravoFilterSelect.vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import type { Issue } from '../../../services/IssuesAPI'
import type { SidebarItem } from './InboxViewsList.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import { useRoute, RouterLink, useRouter } from 'vue-router'
import Tooltip from 'primevue/tooltip'
import BravoDataTable from '@services/ui-component-library/components/BravoDataTable.vue'
import BravoTimestamp from '@services/ui-component-library/components/BravoTimestamp.vue'
import { getCaseIcon, getCaseIconLabel } from '../utils/caseHelper'

// Register the tooltip directive
const vTooltip = Tooltip

// Define props
interface Props {
  selectedView: SidebarItem | null
  isYourInbox?: boolean
  initialLoading?: boolean
  selectedIssueId?: string
  sidebarWidth?: number
}

const props = withDefaults(defineProps<Props>(), {
  isYourInbox: false,
  initialLoading: false,
  selectedIssueId: '',
  sidebarWidth: 280
})
const emit = defineEmits<{
  (e: 'select-issue', issue: Issue): void
  (e: 'create-case'): void
}>()

const casesStore = useCasesStore()
const userStore = useUserStore()
const cases = ref<Issue[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)
const isViewLoading = ref(false)
const route = useRoute()
const router = useRouter()

// Check if we have a view selected
const hasSelectedView = computed(() => !!props.selectedView)

// Find the view details in the store based on selectedView ID
const viewDetails = computed(() => {
  if (!props.selectedView) return null
  
  // Cast the store to access views
  const storeWithViews = casesStore as any
  const views = storeWithViews.views || []
  
  // Find the view with matching ID
  return views.find((view: any) => view.id === props.selectedView?.id)
})

// Handle case selection
const selectCase = (issue: Issue) => {
  emit('select-issue', issue)
}

// Defensive function to ensure we always have a display name
const getCaseDisplayName = (issue: Issue) => {
  return issue.display_name || issue.c__contact_email || issue.m__contact_phone || 'No case name...'
}

// Fetch cases for the selected view
const fetchCasesForView = async () => {
  if (!viewDetails.value) return
  
  isLoading.value = true
  error.value = null
  
  try {
    // Get sort and filter params from the view if they exist
    const sortParams = viewDetails.value.sort || []
    const filterParams = viewDetails.value.filters || []
    
    // Create the params object based on available properties
    const params: any = {}
    
    // Only add sort if it exists
    if (sortParams.length > 0) {
      params.sort = []
      for (const sortParam of sortParams) {
        const [property, direction] = sortParam.split('_')
        params.sort.push({ property: property, direction: direction })
      }
    }
    if (filterParams.length > 0) {
      params.filter = []
      for (const filterParam of filterParams) {
        console.log('filterParam', filterParam);
        params.filter.push({ property: filterParam.filter_field, value: filterParam.filter_compare_field, operator: filterParam.filter_operator })
      }
      console.log('params', params);
   }
    
    // Using any to bypass TypeScript strict checking since we know our API supports these parameters
    // In a production environment, we would properly type this or update the FetchCasesParams interface
    await casesStore.fetchCases(params as any)
    
    // Access the cases from the store
    cases.value = (casesStore as any).issues || []
  } catch (err) {
    console.error('Error fetching cases for view:', err)
    error.value = err instanceof Error ? err.message : 'Failed to load cases'
  } finally {
    isLoading.value = false
  }
}

// Fetch cases for "Your Inbox" with custom owner filter
const fetchCasesForYourInbox = async () => {
  isLoading.value = true
  error.value = null
  
  try {
    // Create custom filter for owner_users_id = current user id AND status in [ready, waiting, 6, 1]
    const params: any = {
      filter: [
        {
          property: 'owner_users_id',
          value: userStore.userData?.id || '',
          operator: 'eq'
        },
        {
          property: 'status',
          value: ['ready', '1'],
          operator: 'in'
        }
      ]
    }
    
    // Using any to bypass TypeScript strict checking since we know our API supports these parameters
    await casesStore.fetchCases(params as any)
    
    // Access the cases from the store
    cases.value = (casesStore as any).issues || []
  } catch (err) {
    console.error('Error fetching cases for Your Inbox:', err)
    error.value = err instanceof Error ? err.message : 'Failed to load cases'
  } finally {
    isLoading.value = false
  }
}

// Track current user ID to avoid refetching on userData refreshes
const currentUserId = ref<string | null>(userStore.userData?.id || null)

// Watch for user ID changes (only when it actually changes, not on refresh)
watch(
  () => userStore.userData?.id,
  (newUserId) => {
    if (newUserId && newUserId !== currentUserId.value) {
      currentUserId.value = newUserId
      // Only refetch if we're on "Your Inbox"
      if (props.isYourInbox) {
        isViewLoading.value = true
        fetchCasesForYourInbox().finally(() => {
          isViewLoading.value = false
        })
      }
    }
  }
)

// Watch for changes to the selected view (separate from user changes)
watch(
  [() => props.selectedView, () => hasSelectedView.value, () => viewDetails.value],
  () => {
    if (props.isYourInbox && currentUserId.value) {
      // For "Your Inbox", create a custom filter with owner = current user id
      isViewLoading.value = true
      fetchCasesForYourInbox().finally(() => {
        isViewLoading.value = false
      })
    } else if (hasSelectedView.value && viewDetails.value) {
      isViewLoading.value = true
      fetchCasesForView().finally(() => {
        isViewLoading.value = false
      })
    }
  },
  { immediate: true }
)

function handleCreateCase() {
  emit('create-case')
}



function getRelativeTime(updated: string | undefined) {
  if (!updated) return '';
  const updatedDate = new Date(updated.replace(/-/g, '/'));
  const now = new Date();
  const diffMs = now.getTime() - updatedDate.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  if (diffMins < 60) return diffMins.toString().padStart(2, '0') + ':' + (diffMs / 1000 % 60).toFixed(0).padStart(2, '0');
  const hours = Math.floor(diffMins / 60);
  const mins = diffMins % 60;
  return hours.toString().padStart(2, '0') + ':' + mins.toString().padStart(2, '0');
}

// Add filter options ref
const filterOptions = ref([
  { label: 'Ready', value: 'ready' },  
  { label: 'Waiting', value: 'waiting' },
  { label: 'Closed', value: 'closed' },
])
const selectedFilter = ref('ready')

// Add sort options
const sortOptions = ref([
  { label: 'Newest', value: 'newest' },
  { label: 'Oldest', value: 'old' },
  { label: 'Intelligent', value: 'intelligent' }
])
const selectedSort = ref('newest')

// Add after the existing refs
const isExpanded = ref(false)
const viewMode = ref<'card' | 'table'>('card')

// Add after the existing computed properties
const expandedLeftPosition = computed(() => {
  return props.sidebarWidth + 56
})

// Add after the existing computed properties
const handleToggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

const handleToggleViewMode = () => {
  viewMode.value = viewMode.value === 'card' ? 'table' : 'card'
}

const handleSelectCase = (event: any) => {
  if (event.data) {
    // Navigate to the case detail while preserving the view parameter
    router.push({
      name: 'inbox-case-detail',
      params: { id: event.data.id },
      query: route.query.view ? { view: route.query.view } : {}
    })
  }
}

// Table columns configuration
const tableColumns = [
  { field: 'display_name', header: 'Case Name', sortable: true },
  { field: 'location.name', header: 'Location', sortable: true },
  { field: 'status', header: 'Status', sortable: true },
  { field: 'updated', header: 'Last Updated', sortable: true }
]
</script>

<template>

  <div :class="['view-card', { expanded: isExpanded }]" :style="{ '--expanded-left': `${expandedLeftPosition}px` }">
    <div v-if="!hasSelectedView" class="empty-state">
      Select a view to get started
    </div>
    
    <div v-else class="view-content">
      <div class="view-header">
        <BravoTitle1 v-if="!props.initialLoading">{{ selectedView?.label }}</BravoTitle1>
        <BravoSkeleton v-else width="150px" height="24px" class="mb-0" />
        <div class="view-actions">
          <BravoButton 
            v-if="isExpanded"
            :icon="viewMode === 'card' ? 'pi pi-table' : 'pi pi-th-large'"
            severity="secondary"
            text
            @click="handleToggleViewMode"
            :aria-label="viewMode === 'card' ? 'Switch to table view' : 'Switch to card view'"
            v-tooltip.top="{
              value: viewMode === 'card' ? 'Table view' : 'Card view',
              showDelay: 400
            }"
          />
          <BravoButton 
            :icon="isExpanded ? 'pi pi-chevron-left' : 'pi pi-chevron-right'"
            severity="secondary"
            text
            @click="handleToggleExpand"
            :aria-label="isExpanded ? 'Collapse view' : 'Expand view'"
            v-tooltip.top="{
              value: isExpanded ? 'Collapse view' : 'Expand view',
              showDelay: 400
            }"
          />
        </div>
      </div>
      
      <div class="filter-container flex justify-between items-center px-4">
        <div class="flex-1">
          <BravoFilterSelect
            v-if="!props.initialLoading"
            v-model="selectedFilter"
            :filterOptions="filterOptions"
            placeholder="Filter cases"
            class="w-36"
            id="case-filter"
            data-test-id="case-filter"
          />
          <BravoSkeleton v-else width="144px" height="32px" border-radius="6px" />
        </div>
        <div class="flex justify-end flex-1">
          <BravoFilterSelect
            v-if="!props.initialLoading"
            v-model="selectedSort"
            :filterOptions="sortOptions"
            placeholder="Sort by"
            class="w-36"
            id="case-sort"
            data-test-id="case-sort"
          />
          <BravoSkeleton v-else width="144px" height="32px" border-radius="6px" />
        </div>
      </div>
      
      <div v-if="props.initialLoading || isViewLoading" class="skeleton-loading-state">
        <div v-for="n in 8" :key="n" class="skeleton-case-row">
          <BravoSkeleton shape="rectangle" width="16px" height="16px" border-radius="6px" class="skeleton-icon" />
          <div class="skeleton-main">
            <BravoSkeleton width="70%" height="16px" class="mb-2" />
            <BravoSkeleton width="50%" height="14px" />
          </div>
          <div class="skeleton-time">
            <BravoSkeleton width="32px" height="12px" class="mb-1" />
            <BravoSkeleton shape="circle" size="8px" />
          </div>
        </div>
      </div>
      
      <div v-else-if="error" class="error-state">
        <p>{{ error }}</p>
        <BravoButton 
          label="Retry" 
          @click="fetchCasesForView"
        />
      </div>
      
      <div v-else-if="cases.length === 0" class="no-cases">
        <p>No cases found for this view</p>
      </div>
      
      <!-- Card View (existing list) -->
      <ul v-else-if="viewMode === 'card'" class="cases-list">
        <RouterLink
          v-for="item in cases"
          :key="item.id"
          :to="{ 
            name: 'inbox-case-detail', 
            params: { id: item.id },
            query: route.query.view ? { view: route.query.view } : {}
          }"
          custom
          v-slot="{ navigate, href, isActive }"
        >
          <li
            :class="['case-list-row group', { 'selected-case': route.params.id === item.id || isActive }]"
            :tabindex="0"
            @click="navigate"
            :href="href"
          >
            <div class="case-list-icon flex items-center justify-center text-xl mr-2">
              <i :class="getCaseIcon(item)" aria-hidden="true"></i>
            </div>
            <div class="case-list-main flex-1 min-w-0">
              <BravoSubhead class="truncate">{{ getCaseDisplayName(item) }}</BravoSubhead>
              <BravoBody class="case-list-subtitle truncate color-secondary">{{ item.c__location || 'No location' }}</BravoBody>
            </div>
            <div class="case-list-time flex flex-col items-end justify-center min-w-[48px]">
              <BravoTimestamp v-if="item.updated" :datetime="item.updated" length="short" class="text-xs text-slate-400 font-medium" />
              <span v-else class="text-xs text-slate-400 font-medium">-</span>
              <span v-if="item.status === 'open'" class="inline-block mt-1 w-2 h-2 rounded-full bg-green-400"></span>
            </div>
          </li>
        </RouterLink>
      </ul>

      <!-- Table View -->
      <div v-else class="table-container">
        <BravoDataTable
          :value="cases"
          :columns="tableColumns"
          :paginator="true"
          :rows="25"
          :rowsPerPageOptions="[10, 25, 50]"
          :rowHover="true"
          dataKey="id"
          :tableStyle="{ minWidth: '50rem' }"
          :stripedRows="false"
          :showGridlines="false"
          selectionMode="single"
          @row-select="handleSelectCase"
          class="cases-table"
        >
          <template #display_name="{ data }">
            <div class="flex items-center gap-2">
              <i :class="getCaseIcon(data)" class="text-lg"></i>
              <span class="font-semibold">{{ getCaseDisplayName(data) }}</span>
            </div>
          </template>
          <template #status="{ data }">
            <span 
              :class="[
                'px-2 py-1 rounded-full text-xs font-medium',
                data.status === 'open' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              ]"
            >
              {{ data.status || 'Unknown' }}
            </span>
          </template>
                      <template #updated="{ data }">
              <BravoTimestamp v-if="data.updated" :datetime="data.updated" length="short" />
              <span v-else>-</span>
            </template>
        </BravoDataTable>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-card {
  background: var(--surface-0);
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 0;
  padding-top: 0;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), z-index 0.3s ease;
  position: relative;
  z-index: 1;
}

.view-card.expanded {
  position: fixed;
  top: 0;
  left: var(--expanded-left);
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--surface-600);
  font-style: italic;
  padding: 2rem;
}

.view-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 0;
  padding-top: 0;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  height: 64px;
  border-bottom: 1px solid var(--border-color);
}

.filter-container {
  height: 48px;
  border-bottom: 1px solid var(--border-color);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  flex: 1;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  gap: 1rem;
  background-color: var(--red-50);
  color: var(--red-600);
  margin: 1rem;
  border-radius: 4px;
}

.no-cases {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--surface-600);
  flex: 1;
}

.cases-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex: 1;
}

.case-list-row {
  display: flex;
  align-items: center;
  padding: 0.75rem .5rem .75rem .25rem;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  background: #fff;
  transition: background 0.15s, border-left 0.15s;
  border-left: 4px solid transparent;
}

.case-list-row:hover {
  background: #f8fafc;
}

.selected-case {
  background: #e8f1fb;
  border-left: 4px solid #2563eb;
}

.case-list-icon {
  flex: 0 0 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  margin-right: 0.75rem;
  color: #64748b;
}

.case-list-main {
  flex: 1 1 auto;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.case-list-title {
  font-weight: 700;
  color: #1e293b;
  font-size: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.case-list-subtitle {
  color: var(--text-color-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.case-list-time {
  flex: 0 0 48px;
  text-align: right;
  color: #94a3b8;
  font-size: 0.95rem;
  font-variant-numeric: tabular-nums;
  font-weight: 500;
}

.table-container {
  flex: 1;
  overflow: auto;
  padding: 1rem;
}

.cases-table {
  width: 100%;
}

.cases-table :deep(.p-datatable-tbody tr) {
  cursor: pointer;
}

.cases-table :deep(.p-datatable-tbody tr:hover) {
  background-color: var(--surface-50);
} 

.skeleton-loading-state {
  padding: 0;
  overflow-y: auto;
  flex: 1;
}

.skeleton-case-row {
  display: flex;
  align-items: center;
  padding: 0.75rem .5rem .75rem .25rem;
  border-bottom: 1px solid #f1f5f9;
  background: #fff;
  width: 100%;
}

.skeleton-icon {
  flex: 0 0 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.skeleton-main {
  flex: 1 1 auto;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-time {
  flex: 0 0 48px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  gap: 0.25rem;
}
</style> 