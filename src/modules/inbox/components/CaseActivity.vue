<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCasesStore } from '../../../stores/cases'
import { useCommunicationsStore } from '../../comms/stores/communications'
import { useUserStore } from '../../../stores/user'
import { useCommsAPI } from '@/composables/services/useCommsAPI'
import { useIssuesAPI } from '@/composables/services/useIssuesAPI'
import type { Issue } from '@/composables/services/useIssuesAPI'
import type { AppConfig as CommsAppConfig } from '@/modules/comms/types'
import type { AvailableComm } from '@/modules/comms/types'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoTabs from '@services/ui-component-library/components/BravoTabs.vue'
import BravoTabList from '@services/ui-component-library/components/BravoTabList.vue'
import BravoTab from '@services/ui-component-library/components/BravoTab.vue'
import BravoTabPanels from '@services/ui-component-library/components/BravoTabPanels.vue'
import BravoTabPanel from '@services/ui-component-library/components/BravoTabPanel.vue'
import BravoMenu from '@services/ui-component-library/components/BravoMenu.vue'
import AllActivityTab from './tabs/AllActivityTab.vue'
import EmailChannel from '../../comms/components/channels/EmailChannel.vue'
import ChatChannel from '../../comms/components/channels/ChatChannel.vue'
import SmsChannel from '../../comms/components/channels/SmsChannel.vue'
import VoiceChannel from '../../comms/components/channels/VoiceChannel.vue'

// Define props
interface Props {
  issueId?: string
  onBack?: () => void
  onViewEvents?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  issueId: undefined,
  onBack: () => {},
  onViewEvents: () => {}
})

const casesStore = useCasesStore()
const store = useCommunicationsStore()
const userStore = useUserStore()
const commsAPI = useCommsAPI()
const issuesAPI = useIssuesAPI()
const route = useRoute()
const router = useRouter()

// Get case ID from route parameters
const routeCaseId = computed(() => route.params.id as string)

// Use current issue from the store if no specific issue ID is provided
const issue = computed(() => {
  if (props.issueId) {
    // If we have an issueId prop, we should fetch and display that
    if (casesStore.currentIssue?.id !== props.issueId) {
      casesStore.fetchCurrentIssue(props.issueId)
    }
  }
  return casesStore.currentIssue
})

// Communication panel state
const isInitializing = ref(false)
const error = ref<string | null>(null)
const loadingCommIds = ref<Set<string>>(new Set())
const isAddingComm = ref(false)

// Rename state management
const renamingCommIds = ref<Set<string>>(new Set())
const renameErrors = ref<Map<string, string>>(new Map())

// Compute active tab from URL query parameter
const selectedTab = computed({
  get: () => {
    const commParam = route.query.comm as string | undefined
    // If we have a comm parameter and it exists in available comms, use it
    if (commParam && availableComms.value.some(comm => comm.id === commParam)) {
      return commParam
    }
    // Otherwise default to all-activity
    return 'all-activity'
  },
  set: (newValue: string) => {
    const query = { ...route.query }
    
    if (newValue === 'all-activity') {
      // Remove comm parameter for all-activity tab
      delete query.comm
    } else {
      // Set comm parameter to the communication ID
      query.comm = newValue
    }
    
    router.replace({ query })
  }
})

// Computed property to get available communications from the case (excluding loading ones)
const availableComms = computed(() => {
  const comms = issue.value?.availableComm || []
  
  // Filter out communications that are still being loaded
  let filteredComms = comms.filter(comm => !loadingCommIds.value.has(comm.id))
  
  // Special case: Hide internal room for specific case sources
  const hideInternalSources = ['web_connect', 'microconnect', 'connect']
  if (issue.value?.source && hideInternalSources.includes(issue.value.source)) {
    filteredComms = filteredComms.filter(comm => comm.object_scope !== 'private')
  }
  
  // Hide Internal/private comms for email cases
  if (issue.value?.source === 'email') {
    filteredComms = filteredComms.filter(comm => comm.object_scope !== 'private')
  }
  
  return filteredComms
})

// Computed property to get the currently selected communication
const selectedComm = computed(() => {
  if (selectedTab.value === 'all-activity') return null
  return availableComms.value.find(comm => comm.id === selectedTab.value) || null
})

// Computed property to determine the selected communication type
const selectedCommType = computed(() => {
  if (!selectedComm.value) return null
  
  const comm = selectedComm.value
  
  // Determine type based on object_scope and comm_type
  if (comm.object_scope === 'email') return 'email'
  if (comm.comm_type === 0) return 'chat'
  if (comm.comm_type === 1) return 'email'
  if (comm.comm_type === 2) return 'sms'
  if (comm.comm_type === 3) return 'voice'
  
  return 'chat' // default
})

// Debug computed to track store state
const debugStoreState = computed(() => {
  return {
    selectedCommId: store.selectedComm?.id,
    activeCommsCount: store.activeComms.length,
    activeCommIds: store.activeComms.map(c => c.id),
    selectedTab: selectedTab.value,
    selectedCommType: selectedCommType.value,
    availableCommsCount: availableComms.value.length
  }
})

// Watch debug state for logging
watch(debugStoreState, (newState) => {
  console.log('🔍 CaseActivity Debug State:', newState)
}, { deep: true })

// Helper function to get communication icon
const getCommIcon = (comm: any) => {
  if (comm.object_scope === 'email') return 'pi pi-envelope'
  if (comm.comm_type === 0) return 'pi pi-comments'
  if (comm.comm_type === 1) return 'pi pi-envelope'
  if (comm.comm_type === 2) return 'pi pi-mobile'
  if (comm.comm_type === 3) return 'pi pi-phone'
  return 'pi pi-comments' // default
}

// Helper function to get communication title
const getCommTitle = (comm: any) => {
  // FIRST: Check for comm_label - if it exists and is not empty, use it
  if (comm.comm_label && comm.comm_label.trim() !== '') {
    return comm.comm_label.trim()
  }
  
  // THEN: Check object_scope and map to appropriate names
  if (comm.object_scope === 'private') {
    return 'Internal'
  }
  
  if (comm.object_scope === 'email') {
    return 'Email'
  }
  
  if (comm.object_scope === 'public') {
    return 'Chat'
  }
  
  // Fallback to original title if no scope matches
  return comm.title || comm.comm_label || `Case # ${comm.object_id?.slice(-6) || 'Unknown'}`
}

// Add communication menu ref and items
const addCommMenu = ref()
const addCommMenuItems = ref([
  {
    label: 'Email',
    icon: 'pi pi-envelope',
    command: () => handleAddCommunication('email')
  },
  {
    label: 'SMS',
    icon: 'pi pi-mobile',
    command: () => handleAddCommunication('sms')
  }
])

// Handler for adding communication
async function handleAddCommunication(type: string) {
  if (!issue.value?.id) {
    console.error('❌ CaseActivity: No issue ID available for adding communication')
    return
  }

  // Set loading state
  isAddingComm.value = true

  try {
    console.log('🚀 CaseActivity: Adding communication of type:', type, 'for issue:', issue.value.id)
    
    // Call the addThread API
    const response = await issuesAPI.addThread({
      id: issue.value.id,
      scope: type as 'email' | 'chat' | 'sms' | 'voice'
    })
    
    console.log('✅ CaseActivity: Successfully added thread:', response)
    
    // Refresh the case data to get the new communication
    await casesStore.fetchCurrentIssue(issue.value.id)
    
    // Find the newly created communication before hiding it
    const allComms = issue.value?.availableComm || []
    const newComm = allComms.find(comm => {
      const commType = comm.object_scope === 'email' ? 'email' : 
                      comm.comm_type === 0 ? 'chat' :
                      comm.comm_type === 1 ? 'email' :
                      comm.comm_type === 2 ? 'sms' :
                      comm.comm_type === 3 ? 'voice' : 'chat'
      return commType === type
    })
    
    if (newComm) {
      console.log('🔄 CaseActivity: Found new communication, hiding until ready:', newComm.id)
      // Hide the new communication until it's fully loaded
      loadingCommIds.value.add(newComm.id)
    }
    
    // Re-initialize communications to pick up the new thread
    await initializeCommunications()
    
    // Now that initialization is complete, show the communication and select it
    if (newComm) {
      console.log('🎯 CaseActivity: Communication ready, showing and selecting tab:', newComm.id)
      
      // Remove from loading set to show the tab
      loadingCommIds.value.delete(newComm.id)
      
      // Wait a tick for the DOM to update
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Select the new communication tab
      selectedTab.value = newComm.id
      
      // Also select it in the store if it's available
      const storeComm = store.activeComms.find(comm => comm.id === newComm.id)
      if (storeComm) {
        await store.selectComm(storeComm.id)
        console.log('✅ CaseActivity: Selected new communication in store:', storeComm.id)
      }
    } else {
      console.warn('⚠️ CaseActivity: Could not find newly created communication of type:', type)
    }
    
    console.log('✅ CaseActivity: Communication added and data refreshed')
    
  } catch (error) {
    console.error('❌ CaseActivity: Failed to add communication:', error)
    // Clean up loading state on error
    if (issue.value?.availableComm) {
      const allComms = issue.value.availableComm
      const failedComm = allComms.find(comm => {
        const commType = comm.object_scope === 'email' ? 'email' : 
                        comm.comm_type === 0 ? 'chat' :
                        comm.comm_type === 1 ? 'email' :
                        comm.comm_type === 2 ? 'sms' :
                        comm.comm_type === 3 ? 'voice' : 'chat'
        return commType === type
      })
      if (failedComm) {
        loadingCommIds.value.delete(failedComm.id)
      }
    }
    // You could add a toast notification here to inform the user
  } finally {
    // Always clear loading state
    isAddingComm.value = false
  }
}

// Handler for showing the menu
function showAddCommMenu(event: Event) {
  addCommMenu.value.toggle(event)
}

// Handler for renaming communication tabs
async function handleCommRename(payload: { value: string | number; oldName: string; newName: string }) {
  const commId = payload.value as string
  const newName = payload.newName.trim()
  
  if (!newName || newName === payload.oldName) {
    console.log('📝 CaseActivity: Rename cancelled or no change')
    return
  }
  
  // Set loading state
  renamingCommIds.value.add(commId)
  renameErrors.value.delete(commId) // Clear any previous errors
  
  try {
    console.log('📝 CaseActivity: Renaming communication:', commId, 'to:', newName)
    
    // Call the store function to update the communication name
    await store.changeCommName(commId, newName)
    
    console.log('✅ CaseActivity: Successfully renamed communication')
    
    // Clear any existing errors for this communication
    renameErrors.value.delete(commId)
    
    // Optionally refresh the case data to ensure consistency
    if (issue.value?.id) {
      await casesStore.fetchCurrentIssue(issue.value.id)
    }
    
  } catch (error) {
    console.error('❌ CaseActivity: Failed to rename communication:', error)
    
    // Set error state
    const errorMessage = error instanceof Error ? error.message : 'Failed to rename communication'
    renameErrors.value.set(commId, errorMessage)
    
  } finally {
    // Clear loading state
    renamingCommIds.value.delete(commId)
  }
}

// Helper functions to get loading and error states for specific communications
const isCommRenaming = (commId: string) => renamingCommIds.value.has(commId)
const getCommRenameError = (commId: string) => renameErrors.value.get(commId) || undefined

// Function to clear rename errors (can be called when user starts editing again)
const clearCommRenameError = (commId: string) => {
  renameErrors.value.delete(commId)
}

// Helper function to get unread count for a specific communication
const getCommUnreadCount = (commId: string): number => {
  const storeComm = store.activeComms.find(comm => comm.id === commId)
  return storeComm?.unreadCount || 0
}

// Debug functions for testing (accessible from browser console)
const debugCurrentState = () => {
  console.log('🐛 CaseActivity DEBUG: Current state:', {
    selectedTab: selectedTab.value,
    commQueryParam: route.query.comm,
    currentlyViewedCommId: store.currentlyViewedCommId,
    selectedCommId: store.selectedComm?.id,
    availableComms: availableComms.value.map(comm => ({
      id: comm.id,
      title: getCommTitle(comm),
      unreadCount: getCommUnreadCount(comm.id)
    }))
  })
}

// Expose debug functions to window for console access
if (typeof window !== 'undefined') {
  (window as any).debugCaseActivity = {
    debugCurrentState
  }
}

const initializeCommunications = async () => {
  if (!issue.value || !userStore.xmppData) {
    console.log('🚫 CaseActivity: No case or XMPP data available')
    return
  }

  isInitializing.value = true
  error.value = null

  try {
    console.log('🚀 CaseActivity: Initializing for case:', issue.value)
    
    // Create CommsAppConfig from user's xmppData
    const commsAppConfig: CommsAppConfig = {
      clientInstance: 'panel-instance',
      csrfToken: 'panel-token',
      serviceConfig: userStore.xmppData,
      requestId: 'panel-request',
      availableComm: [],
      members_locations_id: 'panel-location',
      members_users_id: 'panel-user',
      members_id: 'panel-member'
    }

    // Initialize the communications store if not already initialized
    if (!store.appConfig) {
      console.log('📡 CaseActivity: Initializing communications store...')
      await store.initialize(commsAppConfig)
    }

    // Fetch detailed communication data for each availableComm
    const availableCommsArray = availableComms.value
    console.log('💬 CaseActivity: Found available communications:', availableCommsArray.length)

    for (const availableComm of availableCommsArray) {
      console.log('📡 CaseActivity: Fetching detailed data for comm:', availableComm.id, availableComm.title)
      
      try {
        // Get detailed communication data
        const commData = await commsAPI.getComm({ 
          id: availableComm.id, 
          is_enter: true 
        })
        // here this cmm data!! 
        console.log('📋 CaseActivity: Got detailed comm data:', {
          id: commData.id,
          external_id: commData.external_id,
          title: commData.title,
          comm_type: commData.comm_type,
          object_scope: commData.object_scope,
          cc_recipients: commData.cc_recipients,
          last_sender: commData.last_sender
        })
        
        // Create an AvailableComm object with the detailed data
        const detailedAvailableComm: AvailableComm = {
          id: commData.id,
          object: commData.object,
          object_id: commData.object_id,
          object_scope: commData.object_scope,
          comm_type: commData.comm_type,
          comm_label: commData.comm_label,
          comm_status: commData.comm_status,
          comm_state: commData.comm_state,
          object_source: commData.object_source,
          billing_status: commData.billing_status,
          title: commData.title,
          subtitle: commData.subtitle,
          duration_plus: commData.duration_plus,
          duration: commData.duration,
          message_count: 0, // Default value
          user_message_cnt: commData.user_message_cnt,
          external_rpid: commData.external_rpid,
          external_lpid: commData.external_lpid,
          external_id: commData.external_id,
          external_status: commData.external_status,
          created: commData.created,
          updated: commData.updated,
          occupied: commData.occupied,
          completed: commData.completed,
          c__status: commData.c__status,
          c__state: commData.c__state,
          c__avatar: commData.c__avatar,
          unread_count: availableComm.unread_count || 0, // Use original unread count from case data
          participants: commData.participants?.map(p => ({
            object: p.object,
            object_id: p.object_id,
            name: p.name,
            alias: p.alias,
            external_id: p.external_id,
            host: p.host,
            eligible: p.eligible,
            id: p.id,
            presence: p.presence
          })) || [],
          // Add the additional properties we need for email composer
          ...(commData.cc_recipients && { cc_recipients: commData.cc_recipients }),
          ...(commData.last_sender && { last_sender: commData.last_sender })
        }
        
        // Open the communication in the store
        console.log('➕ CaseActivity: Opening communication in store:', detailedAvailableComm.id)
        await store.openExistingCommWithoutSelection(detailedAvailableComm)
        
      } catch (commError) {
        console.error('❌ CaseActivity: Failed to fetch comm data for:', availableComm.id, commError)
        // Continue with other communications even if one fails
      }
    }
    
    console.log('✅ CaseActivity: Initialization complete')
    
  } catch (err) {
    console.error('❌ CaseActivity: Failed to initialize:', err)
    error.value = err instanceof Error ? err.message : 'Failed to initialize communications'
  } finally {
    isInitializing.value = false
  }
}

const cleanup = () => {
  console.log('🧹 CaseActivity: Cleaning up...')
  try {
    // Reset the currently viewed communication
    store.setCurrentlyViewedComm(null)
    
    // Note: The communications store uses setup syntax and doesn't have $reset()
    // The setCurrentlyViewedComm(null) call above is sufficient for our cleanup needs
    // as it handles the unread count clearing logic
    
    console.log('✅ CaseActivity: Cleanup complete')
  } catch (err) {
    console.error('❌ CaseActivity: Cleanup failed:', err)
  }
}

// Function to handle tab selection
const selectTab = (tabValue: string) => {
  selectedTab.value = tabValue
  console.log('📋 CaseActivity: Selected tab:', tabValue)
  
  // If selecting a communication tab, update the store selection
  if (tabValue !== 'all-activity') {
    const selectedCommData = availableComms.value.find(comm => comm.id === tabValue)
    if (selectedCommData && store.activeComms.length > 0) {
      const storeComm = store.activeComms.find(comm => comm.id === selectedCommData.id)
      if (storeComm) {
        store.selectComm(storeComm.id)
        console.log('📋 CaseActivity: Updated store selection to:', storeComm.id)
      }
    }
  }
}

// Watch for tab changes and update store selection
watch(selectedTab, (newTab, oldTab) => {
  console.log('📋 CaseActivity: Tab changed from', oldTab, 'to', newTab)
  
  // Update the currently viewed communication in the store
  if (newTab !== 'all-activity') {
    // User is viewing a communication tab
    console.log('📋 CaseActivity: Setting currently viewed comm to:', newTab)
    store.setCurrentlyViewedComm(newTab)
    
    const selectedCommData = availableComms.value.find(comm => comm.id === newTab)
    if (selectedCommData && store.activeComms.length > 0) {
      const storeComm = store.activeComms.find(comm => comm.id === selectedCommData.id)
      if (storeComm) {
        console.log('📋 CaseActivity: Updating store selection to:', storeComm.id)
        store.selectComm(storeComm.id)
      } else {
        console.warn('⚠️ CaseActivity: Store communication not found for:', selectedCommData.id)
      }
    } else {
      console.warn('⚠️ CaseActivity: Communication data not found for tab:', newTab)
    }
  } else {
    // User is viewing a non-communication tab (All Activity)
    console.log('📋 CaseActivity: Setting currently viewed comm to null (non-comm tab)')
    store.setCurrentlyViewedComm(null)
    console.log('📋 CaseActivity: Clearing store selection for non-comm tab:', newTab)
  }
})

// Watch for case changes and reinitialize
watch(() => issue.value, (newCase) => {
  if (newCase) {
    // Reset the currently viewed communication when switching cases
    store.setCurrentlyViewedComm(null)
    initializeCommunications()
  } else {
    cleanup()
  }
}, { immediate: true })

// Watch for URL changes and navigate back to All Activity
watch(() => route.params.id, (newCaseId, oldCaseId) => {
  // Only react if the case ID actually changed (not just initial load)
  if (oldCaseId && newCaseId && newCaseId !== oldCaseId) {
    console.log('📋 CaseActivity: Case ID changed in URL from', oldCaseId, 'to', newCaseId, '- navigating to All Activity')
    
    // Navigate back to All Activity tab (this will clear the comm query param)
    selectedTab.value = 'all-activity'
    
    // Reset the currently viewed communication
    store.setCurrentlyViewedComm(null)
    
    // Clean up existing communications
    cleanup()
    
    // The issue watcher above will handle reinitializing when the new case loads
  }
})

// Watch for store selected communication changes and sync tab selection
watch(() => store.selectedComm?.id, (newSelectedId) => {
  console.log('📋 CaseActivity: Store selected comm changed to:', newSelectedId)
  
  // Only sync tab selection if the user has manually selected a communication tab
  // Don't auto-jump from "All Activity" to a communication tab
  if (newSelectedId && availableComms.value.length > 0) {
    const commExists = availableComms.value.find(comm => comm.id === newSelectedId)
    
    // Only sync if we're already on a communication tab (not on all-activity)
    if (commExists && selectedTab.value !== newSelectedId && 
        selectedTab.value !== 'all-activity') {
      console.log('📋 CaseActivity: Would sync tab selection to store, but preventing auto-jump:', newSelectedId)
      // selectedTab.value = newSelectedId // Commented out to prevent auto-jumping
    } else if (commExists && selectedTab.value === 'all-activity') {
      console.log('📋 CaseActivity: Store selected comm but staying on current tab:', selectedTab.value)
    }
  }
})

// Watch for active comms changes to ensure proper initialization
watch(() => store.activeComms.length, (newLength, oldLength) => {
  console.log('📋 CaseActivity: Active comms count changed from', oldLength, 'to', newLength)
  
  // If we have active comms and a selected tab that's a communication, ensure it's selected in store
  if (newLength > 0 && selectedTab.value !== 'all-activity') {
    const selectedCommData = availableComms.value.find(comm => comm.id === selectedTab.value)
    if (selectedCommData) {
      const storeComm = store.activeComms.find(comm => comm.id === selectedCommData.id)
      if (storeComm && store.selectedComm?.id !== storeComm.id) {
        console.log('📋 CaseActivity: Auto-selecting comm in store after initialization:', storeComm.id)
        store.selectComm(storeComm.id)
      }
    }
  }
})

// Watch for available communications changes to handle deep linking
watch(() => availableComms.value.length, (newLength) => {
  console.log('📋 CaseActivity: Available comms count changed to:', newLength)
  
  // If we have a comm query parameter and communications are now available, try to select it
  const commParam = route.query.comm as string | undefined
  if (commParam && newLength > 0) {
    const targetComm = availableComms.value.find(comm => comm.id === commParam)
    if (targetComm) {
      console.log('📋 CaseActivity: Deep linking to communication:', commParam)
      // The selectedTab computed property will automatically handle this
      // but we need to ensure the store is updated when the comm becomes available
      const storeComm = store.activeComms.find(comm => comm.id === commParam)
      if (storeComm) {
        store.selectComm(storeComm.id)
        console.log('📋 CaseActivity: Selected deep-linked communication in store:', storeComm.id)
      }
    } else {
      console.warn('📋 CaseActivity: Deep link comm not found, falling back to all-activity:', commParam)
      // If the comm doesn't exist, redirect to all-activity
      selectedTab.value = 'all-activity'
    }
  }
})

onBeforeUnmount(() => {
  cleanup()
})
</script>

<template>
  <div class="issue-details-container">
    <div v-if="issue" class="h-full">
      <BravoTabs v-model:value="selectedTab" class="activity-tabs" scrollable>
        <div class="tabs-header">
          <BravoTabList>
            <BravoTab value="all-activity">Activity</BravoTab>
            <BravoTab
              v-for="comm in availableComms"
              :key="comm.id"
              :value="comm.id"
              :name="getCommTitle(comm)"
              :renameable="true"
              :rename-loading="isCommRenaming(comm.id)"
              :rename-error="getCommRenameError(comm.id)"
              :badge-value="getCommUnreadCount(comm.id)"
              :badge-visible="getCommUnreadCount(comm.id) > 0"
              badge-severity="primary"
              @rename="handleCommRename"
            >
              <i :class="[getCommIcon(comm), 'mr-2 text-sm']"></i>
              {{ getCommTitle(comm) }}
            </BravoTab>
          </BravoTabList>
          <div class="tabs-header-actions">
            <BravoButton
              text
              severity="secondary"
              :icon="isAddingComm ? 'pi pi-spin pi-spinner' : 'pi pi-plus'"
              :disabled="isAddingComm"
              :aria-label="isAddingComm ? 'Adding Communication...' : 'Add Communication'"
              @click="showAddCommMenu"
            />
            <BravoMenu 
              ref="addCommMenu" 
              :model="addCommMenuItems" 
              :popup="true" 
            />
          </div>
        </div>
        <BravoTabPanels>
          <BravoTabPanel value="all-activity">
            <AllActivityTab :issue-id="issue.id" />
          </BravoTabPanel>
          <BravoTabPanel
            v-for="comm in availableComms"
            :key="comm.id"
            :value="comm.id"
          >
            <div class="comm-content">
              <!-- Error state -->
              <div 
                v-if="error"
                class="h-full flex items-center justify-center text-red-600"
              >
                <div class="text-center">
                  <div class="text-lg font-medium">Error</div>
                  <div class="mt-2">{{ error }}</div>
                </div>
              </div>

              <!-- Loading state -->
              <div 
                v-else-if="isInitializing"
                class="h-full flex items-center justify-center bg-white"
              >
                <div class="text-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <div class="mt-3 text-gray-600 text-sm">Loading communication data...</div>
                </div>
              </div>

              <!-- Communication content -->
              <template v-else-if="store.selectedComm && selectedTab === comm.id">
                <EmailChannel 
                  v-if="selectedCommType === 'email'"
                  :communication="store.selectedComm"
                  :issue="issue"
                />
                <ChatChannel 
                  v-else-if="selectedCommType === 'chat'"
                  :communication="store.selectedComm"
                  :issue="issue"
                />
                <SmsChannel 
                  v-else-if="selectedCommType === 'sms'"
                  :communication="store.selectedComm"
                  :issue="issue"
                />
                <VoiceChannel 
                  v-else-if="selectedCommType === 'voice'"
                  :communication="store.selectedComm"
                  :issue="issue"
                />
                <div v-else class="h-full flex items-center justify-center text-gray-500">
                  <div class="text-center">
                    <div class="text-lg font-medium">Unsupported Communication Type</div>
                    <div class="mt-2">{{ selectedCommType }}</div>
                  </div>
                </div>
              </template>

              <!-- Fallback loading -->
              <div 
                v-else
                class="h-full flex items-center justify-center bg-white"
              >
                <div class="text-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  <div class="mt-3 text-gray-600 text-sm">
                    <template v-if="selectedCommType">
                      Connecting to {{ selectedCommType }}...
                    </template>
                    <template v-else>
                      Loading communication...
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </BravoTabPanel>
        </BravoTabPanels>
      </BravoTabs>
    </div>
  </div>
</template>

<style scoped>
.issue-details-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.activity-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Target the BravoTabs and BravoTabPanel to ensure full height */
:deep(.bravo-tabs) {
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.bravo-tabpanels) {
  flex: 1;
  overflow: hidden;
  padding-bottom: 0 !important; /* Remove any bottom padding */
}

:deep(.p-tabpanels) {
  padding-bottom: 0 !important; /* Remove any bottom padding from PrimeVue default */
}

:deep(.bravo-tabpanel) {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0; /* Remove all padding to ensure full height */
}

/* BravoTabs customizations */
:deep(.bravo-tablist) {
  border-bottom-color: var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1;
  background: white;
  padding-right: 4.5rem; /* Increased space for scroll buttons + plus button */
}

:deep(.p-tablist-next-button) {
  margin-right: 2rem; /* Move scroll button away from plus button */
}

:deep(.p-tablist-prev-button) {
  margin-right: 0.0rem; /* Small gap between prev and next buttons */
}

.tabs-header {
  position: relative;
  padding: 0.61rem 1rem 0rem 1rem;
  background: white;
}

.tabs-header-actions {
  position: absolute;
  top: 0.61rem;
  right: 0.5rem; /* Moved closer to edge to avoid scroll buttons */
  display: flex;
  align-items: center;
  z-index: 2;
}

:deep(.p-tablist-viewport) {
  overflow: hidden;
}

.comm-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0; /* Remove padding to eliminate bottom space */
}
</style> 