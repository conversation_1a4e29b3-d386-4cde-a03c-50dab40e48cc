<script setup lang="ts">
import { ref, computed } from 'vue'
import JourneyWorkflowMFE from './JourneyWorkflowMFE.vue'
import AddJourneyModal from './AddJourneyModal.vue'
import ChangeJourneyModal from './ChangeJourneyModal.vue'
import RemoveJourneyModal from './RemoveJourneyModal.vue'
import BravoButton from '@services/ui-component-library/components/BravoButton.vue'
import BravoMessage from '@services/ui-component-library/components/BravoMessage.vue'
import BravoZeroStateScreen from '@services/ui-component-library/components/BravoZeroStateScreen.vue'
import JourneyZeroStateSvg from '@/assets/journey-zero-state.svg'
import type { Issue } from '../../../services/IssuesAPI'
import type { Journey } from '@/composables/services/useAdminJourneysAPI'
import { useCasesStore } from '@/stores/cases'

const props = withDefaults(defineProps<{
  issue?: Issue
  loading?: boolean
}>(), {
  issue: undefined,
  loading: false
})

const showAddJourneyModal = ref(false)
const showChangeJourneyModal = ref(false)
const showRemoveJourneyModal = ref(false)
const casesStore = useCasesStore()

// Check if the case has an active journey
const hasActiveJourney = computed(() => {
  return props.issue?.journey_id && props.issue?.journey_workflow_id
})

function handleAddJourney() {
  showAddJourneyModal.value = true
}

function handleChangeJourney() {
  showChangeJourneyModal.value = true
}

function handleRemoveJourney() {
  showRemoveJourneyModal.value = true
}

// Handler for Add Journey Modal
async function handleAddJourneySubmit(journey: Journey) {
  if (!props.issue?.id) {
    throw new Error('No case ID available for journey assignment')
  }
  await casesStore.assignJourneyToCase(props.issue.id, journey.id)
}

// Handler for Change Journey Modal
async function handleChangeJourneySubmit(journey: Journey) {
  if (!props.issue?.id) {
    throw new Error('No case ID available for journey change')
  }
  await casesStore.changeJourneyForCase(props.issue.id, journey.id)
}

// Handler for Remove Journey Modal
async function handleRemoveJourneySubmit() {
  if (!props.issue?.id) {
    throw new Error('No case ID available for journey removal')
  }
  await casesStore.removeJourneyFromCase(props.issue.id)
}
</script>

<template>
  <div class="case-journeys-panel">
    <!-- Show Journey Workflow MFE if case has an active journey -->
    <div v-if="hasActiveJourney" class="active-journey">
      <!-- Journey Workflow MFE -->
      <div class="journey-workflow">
        <JourneyWorkflowMFE
          :journey-id="props.issue?.journey_id || ''"
          :workflow-id="props.issue?.journey_workflow_id || ''"
        />
      </div>

      <!-- Journey Management Actions -->
      <div class="journey-actions">
        <BravoButton
          label="Change Journey"
          icon="pi pi-refresh"
          severity="secondary"
          size="small"
          @click="handleChangeJourney"
          class="journey-action-btn"
        />
        <BravoButton
          label="Remove Journey"
          icon="pi pi-times"
          severity="danger"
          size="small"
          outlined
          @click="handleRemoveJourney"
          class="journey-action-btn"
        />
      </div>
    </div>

    <!-- Show CTA to add journey if no active journey -->
    <div v-else class="no-journey-state">
      <BravoZeroStateScreen
        title="No Journey Assigned"
        message="This case doesn't have a journey yet. Journeys help guide experiences through predefined flows and actions, ensuring consistent processes and better outcomes."
        buttonLabel="Add a Journey"
        buttonIcon="pi pi-plus"
        :imageSrc="JourneyZeroStateSvg"
        imageAlt="No Journey Assigned"
        :actionHandler="handleAddJourney"
      />
    </div>

    <!-- Add Journey Modal -->
    <AddJourneyModal
      v-model:visible="showAddJourneyModal"
      :case-id="props.issue?.id"
      :on-add="handleAddJourneySubmit"
    />

    <!-- Change Journey Modal -->
    <ChangeJourneyModal
    cal
      v-model:visible="showChangeJourneyModal"
      :case-id="props.issue?.id"
      :current-journey-id="props.issue?.journey_id"
      :on-change="handleChangeJourneySubmit"
    />

    <!-- Remove Journey Modal -->
    <RemoveJourneyModal
      v-model:visible="showRemoveJourneyModal"
      :case-id="props.issue?.id"
      :on-remove="handleRemoveJourneySubmit"
    />
  </div>
</template>

<style scoped>
.case-journeys-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.active-journey {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.journey-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid var(--surface-200);
  justify-content: space-evenly;
}

.journey-action-btn {
  flex-shrink: 0;
}

.journey-workflow {
  flex: 1;
  overflow: hidden;
}

.no-journey-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.journey-info {
  margin-top: 1rem;
}

.journey-info :deep(.p-message-wrapper) {
  justify-content: center;
}
</style> 