<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useCasesStore } from '../../../../stores/cases'
import BravoTimeline from '@services/ui-component-library/components/BravoTimeline.vue'
import BravoAvatar from '@services/ui-component-library/components/BravoAvatar.vue'
import BravoBody from '@services/ui-component-library/components/BravoTypography/BravoBody.vue'
import BravoSkeleton from '@services/ui-component-library/components/BravoSkeleton.vue'
import BravoTag from '@services/ui-component-library/components/BravoTag.vue'
import { useInteractionEventsAPI, type InteractionEvent } from '@/composables/services/useInteractionEventsAPI'
import { getEventIcon } from '../../utils/eventHelpers'
import automationAvatar from '@/assets/automationavatar.png'
import { 
  formatEventTitle, 
  formatEventContent, 
  type ExtendedInteractionEvent,
  type StructuredContent 
} from '../../utils/activityEventHelpers'

interface Props {
  issueId?: string
}

const props = defineProps<Props>()

const casesStore = useCasesStore()
const interactionEventsAPI = useInteractionEventsAPI()
const route = useRoute()

// Local state for interaction events
const interactionEvents = ref<InteractionEvent[]>([])
const loadingEvents = ref(false)
const eventsError = ref<string | null>(null)

// Get case ID from route parameters or props
const caseId = computed(() => props.issueId || route.params.id as string)

// Use current issue from the store
const issue = computed(() => casesStore.currentIssue)

// Helper function to create avatar data from user information
const createAvatarData = (user: any) => {
  let fullName = user?.users_full_name || user?.name || ''
  const avatar = user?.user_avatar
  
  // Check if this is automation user data (all fields empty/null)
  const isAutomation = user && (
    (!user.first_name || user.first_name === '') &&
    (!user.last_name || user.last_name === '') &&
    (!user.users_full_name || user.users_full_name === null) &&
    (!user.users_id || user.users_id === '') &&
    (!user.users_nickname || user.users_nickname === '')
  )
  
  if (isAutomation) {
    return {
      firstName: 'A',
      lastName: 'U',
      image: automationAvatar, // Use imported automation avatar
      randomBackground: false // Don't use random background when we have a specific image
    }
  }
  
  // If we don't have good user data, try to get it from the case store
  if (!fullName || fullName === 'Unknown User') {
    const currentIssue = casesStore.currentIssue
    if (currentIssue) {
      // Try case owner user name
      if ((currentIssue as any).c__owner_user_name) {
        fullName = (currentIssue as any).c__owner_user_name
      } else if (currentIssue.memberUser) {
        // Try member user data
        const memberFirstName = currentIssue.memberUser.first_name || ''
        const memberLastName = currentIssue.memberUser.last_name || ''
        const memberFullName = `${memberFirstName} ${memberLastName}`.trim()
        if (memberFullName) {
          fullName = memberFullName
        }
      }
    }
  }
  
  const nameParts = fullName.split(' ')
  const firstName = nameParts[0] || ''
  const lastName = nameParts[1] || ''
  
  return {
    firstName,
    lastName,
    image: avatar || undefined,
    randomBackground: !avatar // Use random background if no image
  }
}

// Helper function to check if content is structured
const isStructuredContent = (content: any): content is StructuredContent => {
  return content && typeof content === 'object' && content.isStructured === true
}

// Helper function to map tag states to BravoTag states
const getTagState = (tagState?: string): 'new' | 'ready' | 'waiting' | 'resolved' | 'closed' | 'draft' | 'published' | 'unpublished' | 'previously-published' | 'archived' => {
  const state = tagState?.toLowerCase()
  
  // Map to valid BravoTag states
  switch (state) {
    case 'waiting':
      return 'waiting'
    case 'ready':
      return 'ready' 
    case 'resolved':
      return 'resolved'
    case 'closed':
      return 'closed'
    default:
      return 'new' // Default fallback
  }
}


// Computed property to format events for BravoTimeline
const timelineEvents = computed(() => {
  return interactionEvents.value.map((event, index) => {
    // Cast to ExtendedInteractionEvent to access additional properties
    const extendedEvent = event as ExtendedInteractionEvent
    
    return {
      title: formatEventTitle(extendedEvent, caseId.value),
      user: extendedEvent.refs?.user?.users_full_name || 'Unknown User',
      datetime: extendedEvent.created,
      content: formatEventContent(extendedEvent, caseId.value) as any, // Pass caseId for file lookups
      icon: getEventIcon(extendedEvent.type?.id || extendedEvent.category?.id),
      avatar: createAvatarData(extendedEvent.refs?.user)
    }
  })
})

// Helper function to get file download URL
const getFileDownloadUrl = (fileItem: any) => {
  // Use the same approach as CaseFilesList - directly use the file.file property
  return fileItem.file
}

// Helper function to open file (same as CaseFilesList)
const openFile = (fileItem: any) => {
  window.open(fileItem.file, '_blank')
}

// Helper function to get file type for icon/preview
const getFileType = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image'
  } else if (['pdf'].includes(extension)) {
    return 'pdf'
  } else if (['doc', 'docx'].includes(extension)) {
    return 'document'
  } else if (['xls', 'xlsx'].includes(extension)) {
    return 'spreadsheet'
  }
  
  return 'file'
}

// Helper function to format file size
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}



// Fetch interaction events for the current issue
async function fetchInteractionEvents() {
  const currentCaseId = caseId.value
  if (!currentCaseId) {
    console.warn('⚠️ AllActivityTab: Cannot fetch events: No case ID available');
    return;
  }

  loadingEvents.value = true;
  eventsError.value = null;

  try {
    // Ensure the current issue is loaded if it's not already
    if (!casesStore.currentIssue || casesStore.currentIssue.id !== currentCaseId) {
      await casesStore.fetchCurrentIssue(currentCaseId)
    }

    // Also ensure files are loaded for file-added events
    if (casesStore.files.length === 0) {
      try {
        await casesStore.fetchFiles(currentCaseId)
      } catch (fileError) {
        console.warn('⚠️ AllActivityTab: Could not load files for case:', fileError)
        // Continue anyway - file events will just show basic content
      }
    }

    console.log('📞 AllActivityTab: Fetching interaction events for case:', currentCaseId);
    const filters = [
      {"property":"source_object_id","value": currentCaseId},
      {"property":"source_object","value":"issues"}
    ];
    
    interactionEvents.value = await interactionEventsAPI.fetchInteractionEvents({
      issueId: currentCaseId,
      filter: filters
    });
    
    console.log('✅ AllActivityTab: Interaction events loaded:', interactionEvents.value.length);
  } catch (err) {
    console.error('❌ AllActivityTab: Error fetching interaction events:', err);
    eventsError.value = err instanceof Error ? err.message : 'An error occurred';
  } finally {
    loadingEvents.value = false;
  }
}

// Watch for case ID changes and reload activity
watch(caseId, (newCaseId, oldCaseId) => {
  console.log('🔍 AllActivityTab: Case ID changed from', oldCaseId, 'to', newCaseId);
  if (newCaseId && newCaseId !== oldCaseId) {
    console.log('🔄 AllActivityTab: Reloading activity due to case ID change');
    fetchInteractionEvents();
  }
}, { immediate: true })

// Watch for case updates (like resolve, escalate, etc.) and refresh events
watch(() => issue.value?.updated, (newUpdated, oldUpdated) => {
  console.log('🔄 AllActivityTab: Issue updated timestamp changed from', oldUpdated, 'to', newUpdated);
  if (newUpdated && oldUpdated && newUpdated !== oldUpdated) {
    console.log('🔄 AllActivityTab: Refreshing events due to case update');
    fetchInteractionEvents();
  }
})

// Listen for case activity refresh events from the store
function handleCaseActivityRefresh(event: CustomEvent) {
  const { caseId: refreshCaseId } = event.detail;
  console.log('🔄 AllActivityTab: Received activity refresh event for case:', refreshCaseId);
  
  // Only refresh if this is for the current case
  if (refreshCaseId === caseId.value) {
    console.log('🔄 AllActivityTab: Refreshing events due to store activity refresh');
    fetchInteractionEvents();
  }
}

onMounted(() => {
  fetchInteractionEvents()
  
  // Add event listener for case activity refresh
  window.addEventListener('case-activity-refresh', handleCaseActivityRefresh as EventListener)
})

// Clean up event listener on unmount
onUnmounted(() => {
  window.removeEventListener('case-activity-refresh', handleCaseActivityRefresh as EventListener)
})
</script>

<template>
  <div class="all-activity-content">
    <div v-if="loadingEvents">
      <!-- Timeline skeleton loader -->
      <div class="timeline-skeleton">
        <div v-for="i in 4" :key="'timeline-skeleton-' + i" class="timeline-skeleton-item">
          <div class="timeline-skeleton-marker">
            <!-- Icon skeleton -->
            <BravoSkeleton shape="circle" size="32px" class="timeline-skeleton-icon" />
            <!-- Connector line -->
            <div v-if="i < 4" class="timeline-skeleton-connector"></div>
          </div>
          <div class="timeline-skeleton-content">
            <!-- Title skeleton -->
            <BravoSkeleton :width="Math.random() * 80 + 120 + 'px'" height="20px" class="mb-2" />
            <!-- Content skeleton -->
            <BravoSkeleton :width="Math.random() * 100 + 200 + 'px'" height="16px" class="mb-1" />
            <BravoSkeleton :width="Math.random() * 150 + 150 + 'px'" height="16px" class="mb-2" />
            <!-- Meta info skeleton -->
            <BravoSkeleton :width="Math.random() * 60 + 100 + 'px'" height="14px" />
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="eventsError">{{ eventsError }}</div>
    <div v-else-if="interactionEvents.length === 0">No activity found.</div>
    <div v-else class="activity-timeline-container">
      <BravoTimeline 
        :value="timelineEvents" 
        :useAvatars="true"
        :compact="true"
        align="left"
      >
        <template #item-content="{ item }">
          <!-- Handle structured content with BravoTag components and files -->
          <BravoBody v-if="isStructuredContent(item.content)">
            <template v-for="(part, index) in item.content.parts" :key="index">
              <!-- Render BravoTag for tag content -->
              <BravoTag 
                v-if="part.type === 'tag'"
                :value="part.value"
                :state="getTagState(part.tagState)"
              />
              <!-- Render file preview for file content -->
              <div 
                v-else-if="part.type === 'file' && part.fileData" 
                class="file-preview-container"
                @click="() => openFile(part.fileData)"
              >
                <div class="file-preview">
                  <div class="file-info">
                    <!-- Show image preview or file icon -->
                    <div class="file-icon">
                      <img 
                        v-if="getFileType(part.value) === 'image' && part.fileData.thumbnail"
                        :src="part.fileData.thumbnail" 
                        :alt="part.value"
                        class="file-thumbnail"
                      />
                      <i 
                        v-else
                        :class="{
                          'pi pi-file-pdf': getFileType(part.value) === 'pdf',
                          'pi pi-image': getFileType(part.value) === 'image',
                          'pi pi-file-word': getFileType(part.value) === 'document',
                          'pi pi-file-excel': getFileType(part.value) === 'spreadsheet',
                          'pi pi-file': getFileType(part.value) === 'file'
                        }"
                        class="text-xl"
                      ></i>
                    </div>
                    <div class="file-details">
                      <div class="file-name">{{ part.value }}</div>
                      <div class="file-meta">
                        <span class="file-size">{{ formatFileSize(part.fileData.size) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Render text content -->
              <span v-else v-html="part.value"></span>
            </template>
          </BravoBody>
          <!-- Handle regular HTML content -->
          <!-- eslint-disable-next-line vue/no-v-text-v-html-on-component -->
          <BravoBody v-else v-html="item.content"></BravoBody>
        </template>
      </BravoTimeline>
    </div>
  </div>
</template>

<style scoped>
.all-activity-content {
  color: var(--surface-600);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.activity-timeline-container {
  padding: 1rem 1rem 0rem 1rem;
  overflow-y: auto;
  flex: 1;
}

/* Style bold elements within timeline content to be less bold */
.activity-timeline-container :deep(strong),
.activity-timeline-container :deep(b) {
  font-weight: 400;
}

/* Additional targeting for timeline content */
:deep(.bravo-timeline strong),
:deep(.bravo-timeline b) {
  font-weight: 400;
}

/* File preview styles */
.file-preview-container {
  margin: 0.5rem 0;
  display: inline-block;
  cursor: pointer;
}

.file-preview {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: var(--surface-50);
  border: 1px solid var(--surface-200);
  border-radius: 6px;
  transition: background-color 0.2s;
  min-width: fit-content;
}

.file-preview:hover {
  background: var(--surface-100);
  border-color: var(--primary-color);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  margin-right: 0.75rem;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
}

.file-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.file-thumbnail:hover {
  transform: scale(1.05);
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.file-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}





.timeline-skeleton {
  padding: 1rem;
}

.timeline-skeleton-item {
  display: flex;
  margin-bottom: 2rem;
  position: relative;
}

.timeline-skeleton-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 1rem;
  position: relative;
}

.timeline-skeleton-connector {
  width: 2px;
  height: 2rem;
  background-color: #e2e8f0;
  margin-top: 0.5rem;
}

.timeline-skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 0.25rem;
}
</style> 