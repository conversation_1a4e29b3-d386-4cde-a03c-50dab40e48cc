<script setup lang="ts">
import BravoPopover from '@services/ui-component-library/components/BravoPopover.vue';
import <PERSON><PERSON>elect<PERSON>ield from '@services/ui-component-library/components/BravoSelectField.vue';
import BravoButton from '@services/ui-component-library/components/BravoButton.vue';
import FroalaEditor from 'froala-editor';

import { watch, ref, computed, onMounted} from 'vue';

const visible = defineModel<boolean>('visible');
const tokenPopover = ref(null);

const props = defineProps<{
  editor: any;
  tokens: Array<{ id: string; label: string }>;
  initialtokenId: HTMLElement | null;
}>();

const togglePopUp = () => {
  const toolBarEL = props.editor.$tb.find('[data-cmd="insertToken"]')[0];
  if(tokenPopover.value && toolBarEL){
    tokenPopover.value.toggle(event, toolBarEL);
  }
};

const hidePopUp = () => {
  if(tokenPopover.value){
    tokenPopover.value.hide();
  }
};

watch(visible, (newVal) => {
  if (newVal) {
    togglePopUp();
    if (props.initialtokenId) {
      const tokenId = props.initialtokenId.getAttribute("data-token-id");
      selectedTokenId.value = tokenId;
    }
  } else{
    hidePopUp();
  }
});

const buttonLabel = computed(() => {
  return props.initialtokenId ? 'Update Token' : 'Insert Token';
});

const selectedTokenId = ref<string | null>(null);

const onInsertClick = () => {
  const fullToken = props.tokens.find((t) => t.id === selectedTokenId.value);
 
  if (fullToken) {
    FroalaEditor.PLUGINS.token().insertToken(props.initialtokenId, props.editor, fullToken.id, fullToken.label);
  }
  selectedTokenId.value = null;
  togglePopUp();
};

const onCancelClick = () => {
  selectedTokenId.value = null;
  togglePopUp();
};
</script>

<template>
  <BravoPopover 
    ref="tokenPopover" 
    v-model:visible="visible" 
    dismissable 
    position="right"
    class="w-[320px] max-h-[280px] overflow-hidden shadow-xl rounded-xl bg-white border border-gray-100"
  >
    <div class="relative">

      <!-- Content -->
      <div class="px-5 py-4 space-y-4 max-h-[150px] overflow-y-auto">
        <!-- Select Field with enhanced styling -->
        <div class="space-y-2">
          <label for="token-select" class="block text-sm font-medium text-gray-700">
            Available Tokens
          </label>
          <div class="relative">
            <BravoSelectField 
              v-model="selectedTokenId" 
              :options="props.tokens" 
              id="token-select" 
              data-test-id="token-select"
              option-label="label" 
              option-value="id" 
              placeholder="Select a token..."
              class="w-full"
            />
          </div>
        </div>
      </div>

      <!-- Footer Actions -->
      <div class="px-2 py-4 bg-white border-t border-gray-100 flex justify-between items-center gap-3">
        <BravoButton 
          label="Cancel" 
          severity="secondary" 
          text 
          @click="onCancelClick"
          class="text-gray-600 hover:text-gray-800 hover:bg-gray-100 px-4 py-2 rounded-lg font-medium transition-colors"
        />
        
        <BravoButton 
          :label="buttonLabel" 
          severity="primary"
          @click="onInsertClick" 
          :disabled="!selectedTokenId"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-medium transition-colors shadow-sm"
        />
      </div>
    </div>
  </BravoPopover>
</template>

<style scoped>
/* Custom scrollbar styling */
.max-h-\[150px\]::-webkit-scrollbar {
  width: 6px;
}

.max-h-\[150px\]::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.max-h-\[150px\]::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.max-h-\[150px\]::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>