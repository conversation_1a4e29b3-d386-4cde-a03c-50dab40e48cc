import { useDataDogRUM } from '@/composables/useDataDogRUM'
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteLocationNormalized } from 'vue-router'
import { useKnowledgeStore } from '../modules/knowledge/stores/knowledge'
import NotFound from '../views/404NotFound.vue'
import Login from '../views/Login.vue'
import ForgotPassword from '../views/ForgotPassword.vue'
import ResetPassword from '../views/ResetPassword.vue'
import { useAuthStore } from '@/stores/auth'

// Get the base URL from environment variables
const BASE_URL = import.meta.env.VITE_APP_BASE_URL || '/';

const { init, trackPageView, addAction, addError } = useDataDogRUM();

const router = createRouter({
  history: createWebHistory(BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: Login,
      meta: {
        title: 'Login - OvationCXM'
      },
      // Redirect to home if already authenticated
      beforeEnter: async (to, from, next) => {
        const authStore = useAuthStore()

        // Make sure auth store is initialized
        if (!authStore.isInitialized) {
          await authStore.initialize()
        }

        // Redirect to home if already authenticated
        if (authStore.isAuthenticated) {
          console.debug('Already authenticated, redirecting to home')
          next('/')
        } else {
          // Allow access to login page
          next()
        }
      }
    },
    {
      path: '/',
      component: () => import('../views/RootLayout.vue'),
      // Protect all routes under root
      beforeEnter: async (to, from, next) => {
        const authStore = useAuthStore()

        // Make sure auth store is initialized
        if (!authStore.isInitialized) {
          await authStore.initialize()
        }

        if (authStore.isAuthenticated) {
          next()
        } else {
          console.debug('Not authenticated, redirecting to login')
          next('/login')
        }
      },
      children: [
        {
          path: '',
          redirect: '/inbox'
        },
        {
          path: 'inbox',
          name: 'inbox',
          component: () => import('../modules/inbox/views/InboxView.vue'),
          meta: {
            title: 'Inbox - OvationCXM'
          },
          children: [
            {
              path: 'cases/:id',
              name: 'inbox-case-detail',
              component: () => import('../modules/inbox/views/InboxCase.vue'),
            },
            {
              path: 'tasks/:id',
              name: 'inbox-task-detail',
              component: () => import('../modules/inbox/views/InboxTask.vue'),
            },
            {
              path: 'task/:id',
              name: 'inbox-task-detail',
              component: () => import('../modules/inbox/views/InboxTask.vue'),
            }
          ]
        },
        {
          path: 'journeys',
          name: 'journeys',
          component: () => import('../views/JourneysView.vue'),
          meta: {
            title: 'Journeys - OvationCXM'
          }
        },
        {
          path: 'knowledge',
          name: 'knowledge',
          component: () => import('../modules/knowledge/views/KnowledgeView.vue'),
          meta: {
            title: 'Knowledge - OvationCXM'
          },
          children: [
            {
              path: '',
              name: 'Knowledge',
              component: () => import('../modules/knowledge/components/KnowledgeList.vue'),
              meta: {
                title: 'Knowledge - OvationCXM'
              }
            },
            {
              path: 'articles/:id',
              name: 'knowledge-article',
              component: () => import('../modules/knowledge/views/ArticleView.vue'),
              props: true,
              meta: {
                title: 'Article'
              }
            }
          ]
        },
        {
          path: 'analytics',
          name: 'analytics',
          component: () => import('../views/AnalyticsView.vue'),
          meta: {
            title: 'Analytics - OvationCXM'
          }
        },
        {
          path: 'settings',
          name: 'settings',
          redirect: '/settings/home',
          meta: {
            title: 'Settings - OvationCXM'
          }
        },
        {
          path: 'settings/:section',
          name: 'settings-section',
          component: () => import('../modules/settings/views/SettingsView.vue'),
          props: true,
          meta: {
            title: (route: RouteLocationNormalized) => {
              const section = route.params.section;
              if (section) {
                // Format section for display (capitalize and replace dashes with spaces)
                const formattedSection = section.toString()
                  .split('-')
                  .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(' ');
                return `${formattedSection} Settings - OvationCXM`;
              }
              return 'Settings - OvationCXM';
            }
          }
        },
        {
          path: 'profile',
          name: 'profile',
          component: () => import('../views/UserProfile.vue'),
          meta: {
            title: 'User Profile - OvationCXM'
          }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: NotFound,
      meta: {
        title: '404 - Page Not Found'
      }
    },
    {
      path: '/passwordreset',
      name: 'passwordreset',
      component: ForgotPassword,
      meta: {
        title: 'Forgot Password - OvationCXM'
      },
      // Redirect to home if already authenticated
      beforeEnter: async (to, from, next) => {
        const authStore = useAuthStore()

        // Make sure auth store is initialized
        if (!authStore.isInitialized) {
          await authStore.initialize()
        }

        // Redirect to home if already authenticated
        if (authStore.isAuthenticated) {
          console.debug('Already authenticated, redirecting to login')
          next('/login')
        } else {
          // Allow access to login page
          next()
        }
      }
    },
    {
      path: '/passwordchange/:token/:email',
      name: 'passwordchange',
      component: ResetPassword,
      meta: {
        title: 'Change Password - OvationCXM'
      },
      beforeEnter: async (to, from, next) => {
        const authStore = useAuthStore()

        // Wait for the auth store to initialize if needed
        if (!authStore.isInitialized) {
          await authStore.initialize()
        }

        // Redirect to login if already authenticated
        if (authStore.isAuthenticated) {
          console.debug('Already authenticated, redirecting to login')
          next('/login')
        } else {
          next()
        }
      }
    },
  ]
})

router.beforeEach((to, from, next) => {
  // If we're navigating away from the knowledge base section
  if (from.path.startsWith('/knowledge') && !to.path.startsWith('/knowledge')) {
    const knowledgeStore = useKnowledgeStore()
    knowledgeStore.clearCurrentList()
    knowledgeStore.clearCurrentArticle()
  }
  // If we're navigating away from a specific article
  else if (from.name === 'knowledge-article' && to.name !== 'knowledge-article') {
    const knowledgeStore = useKnowledgeStore()
    knowledgeStore.clearCurrentArticle()
  }

  // Update document title based on route meta
  if (to.meta.title) {
    // Handle function or string title
    const title = typeof to.meta.title === 'function'
      ? to.meta.title(to)
      : to.meta.title;
    document.title = title as string;
  } else {
    // Default title
    document.title = 'OvationCXM'
  }

  next()
});

router.afterEach((to, from, failure) => {
  if (!failure) {
    // Track page view with route name and path
    trackPageView(to.path, to.name?.toString());

    // Log navigation action
    addAction('navigation', {
      from: from.fullPath,
      to: to.fullPath,
      routeName: to.name?.toString(),
    });
  } else {
    // Log navigation error if navigation fails
    addError(new Error('Navigation failed'), {
      from: from.fullPath,
      to: to.fullPath,
      reason: failure.message || 'Unknown',
    });
  }
});

export default router
