import { getHttpClient } from './httpClientProvider';
import type { AuthStoreInterface } from '@/stores/auth';
import type { SSOCheckResponse, UserProfileData, UserResponse } from '@/composables/services/useUserAPI';

/**
 * @deprecated Use the useUserAPI composable from '@/composables/services' instead
 * This class is kept for backward compatibility but should not be used in new code
 */
class UserAPI {
    constructor() {
        console.warn('UserAPI is deprecated. Use useUserAPI composable from @/composables/services instead.');
    }

    private logDeprecationWarning(methodName: string): any {
        console.warn(
            `UserAPI.${methodName} is deprecated. Use useUser<PERSON>I composable from @/composables/services instead.`
        );
        return { success: false, message: 'Method deprecated. Use useUserAPI composable.' };
    }

    async checkSSO(email: string): Promise<SSOCheckResponse> {
        return this.logDeprecationWarning('checkSSO');
    }

    async updateUserProfile(profileData: UserProfileData): Promise<UserResponse> {
        return this.logDeprecationWarning('updateUserProfile');
    }

    async getPartnerMetadata(): Promise<any> {
        return this.logDeprecationWarning('getPartnerMetadata');
    }
}

export { UserAPI };
export type { SSOCheckResponse, UserProfileData, UserResponse };
export const userAPI = new UserAPI();
